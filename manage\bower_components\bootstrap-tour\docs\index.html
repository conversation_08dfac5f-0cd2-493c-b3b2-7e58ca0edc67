<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <title>Bootstrap Tour, easy product tours with Bootstrap from Twitter</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="assets/vendor/bootstrap.min.css" rel="stylesheet">
    <!-- <link href="assets/vendor/bootstrap-theme.min.css" rel="stylesheet"> -->
    <link href="assets/vendor/prism.css" rel="stylesheet">
    <link href="assets/css/bootstrap-tour.css" rel="stylesheet">
    <link href="assets/css/index.css" rel="stylesheet">
    <!--[if lt IE 9]><script src="assets/vendor/html5shiv.js"></script><![endif]-->
  </head>
  <body>
    <a href="https://github.com/sorich87/bootstrap-tour" id="github"><img src="https://s3.amazonaws.com/github/ribbons/forkme_right_darkblue_121621.png" alt="Fork me on GitHub"></a>
    <div id="navbar" class="navbar navbar-inverse navbar-fixed-top">
      <div class="container">
        <button type="button" class="navbar-toggle pull-right" data-toggle="collapse" data-target=".navbar-collapse">
          <span class="icon-bar"></span>
          <span class="icon-bar"></span>
          <span class="icon-bar"></span>
        </button>
        <a href="/" class="navbar-brand">Bootstrap Tour</a>
        <h4 class="navbar-text pull-right">
          <a href="http://getbootstrap.com" target="_blank" class="label label-info">Bootstrap 3 ready!</a>
        </h4>
        <div class="collapse navbar-collapse">
          <ul class="navbar-nav nav">
            <li>
              <a href="#usage">Usage</a>
            </li>
            <li class="dropdown">
              <a href="#" class="dropdown-toggle" data-toggle="dropdown">Options <span class="caret"></span></a>
              <ul class="dropdown-menu">
                <li>
                  <a href="#options">Tour options</a>
                </li>
                <li>
                  <a href="#step-options">Step options</a>
                </li>
              </ul>
            </li>
            <li>
              <a href="#methods">Methods</a>
            </li>
            <li>
              <a href="#contributing">Contributing</a>
            </li>
            <li>
              <a href="#license">License</a>
            </li>
          </ul>
        </div>
      </div>
    </div>
    <header id="home">
      <div class="jumbotron masthead">
        <h1 id="reflex">Bootstrap Tour</h1>
        <p>Quick and easy way to build your product tours with Twitter Bootstrap Popovers.</p>
        <p>
          <a href="https://github.com/sorich87/bootstrap-tour/archive/master.zip" id="download" class="btn-primary btn-large btn"><span class="glyphicon glyphicon-download"></span> Download</a>
          <button type="button" id="demo" class="btn btn-default" data-demo><span class="glyphicon glyphicon-play"></span> Demo</button>
        </p>
        <ul class="masthead-links">
          <li>
            <a href="http://github.com/sorich87/bootstrap-tour">GitHub project</a>
          </li>
          <li>
            Version 0.8.0
          </li>
          <li>
            Bootstrap version &lt;= 3.0.0
          </li>
        </ul>
      </div>
    </header>
    <section id="usage">
      <div class="container">
        <div class="page-header">
          <h2>Usage <small>Oh wait, is it so simple?</small></h2>
        </div>
        <h3>Add the dependencies</h3>
<pre class="language-markup"><code>&lt;!DOCTYPE html&gt;
&lt;html&gt;
&lt;head&gt;
    ...
    &lt;link href="bootstrap-tour.css" rel="stylesheet"&gt;
    &lt;!--[if lt IE 9]&gt;&lt;script src="html5shiv.js"&gt;&lt;![endif]--&gt;
&lt;/head&gt;
&lt;body&gt;
    ...
    &lt;script src="jquery.js"&gt;&lt;/script&gt;
    &lt;script src="bootstrap.tooltip.js"&gt;&lt;/script&gt;
    &lt;script src="bootstrap.popover.js"&gt;&lt;/script&gt;
    &lt;script src="bootstrap-tour.js"&gt;&lt;/script&gt;
&lt;/body&gt;
&lt;/html&gt;</code></pre>
        <h3>Setup your tour</h3>
<pre class="language-javascript"><code>// Instance the tour
var tour = new Tour();

// Add your steps. Not too many, you don't really want to get your users sleepy
tour.addSteps([
  {
    element: "#my-element", // string (jQuery selector) - html element next to which the step popover should be shown
    title: "Title of my step", // string - title of the popover
    content: "Content of my step" // string - content of the popover
  },
  {
    element: "#my-other-element",
    title: "Title of my step",
    content: "Content of my step"
  }
]);

// Initialize the tour
tour.init();

// Start the tour
tour.start();</code></pre>
      </div>
    </section>
    <section id="options">
      <div class="container">
        <div class="page-header">
          <h2>Options <small>Well, let's go deeper</small></h2>
        </div>
        <h3 id="tour-options">Tour options</h3>
<pre class="language-javascript"><code>var tour = new Tour({
  name: "tour",
  container: "body",
  keyboard: true,
  storage: window.localStorage,
  debug: false,
  backdrop: false,
  redirect: true,
  orphan: false,
  duration: false,
  basePath: "",
  template: "&lt;div class='popover tour'&gt;
    &lt;div class='arrow'&gt;&lt;/div&gt;
    &lt;h3 class='popover-title'&gt;&lt;/h3&gt;
    &lt;div class='popover-content'&gt;&lt;/div&gt;
    &lt;div class='popover-navigation'&gt;
        &lt;button class='btn btn-default' data-role='prev'&gt;&laquo; Prev&lt;/button&gt;
        &lt;span data-role='separator'&gt;|&lt;/span&gt;
        &lt;button class='btn btn-default' data-role='next'&gt;Next &raquo;&lt;/button&gt;
    &lt;/div&gt;
    &lt;button class='btn btn-default' data-role='end'&gt;End tour&lt;/button&gt;
    &lt;/nav&gt;
  &lt;/div&gt;",
  afterGetState: function(key, value) {},
  afterSetState: function(key, value) {},
  afterRemoveState: function(key, value) {},
  onStart: function(tour) {},
  onEnd: function(tour) {},
  onShow: function(tour) {},
  onShown: function(tour) {}
  onHide: function(tour) {},
  onHidden: function(tour) {},
  onNext: function(tour) {},
  onPrev: function(tour) {},
  onPause: function(tour, duration) {},
  onResume: function(tour, duration) {}
});</code></pre>
        <table class="table  table-bordered table-striped">
          <thead>
            <tr>
              <th>Name</th>
              <th>Type</th>
              <th>Description</th>
              <th>Default</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>name</td>
              <td>String</td>
              <td>This option is used to build the name of the
              storage item where the tour state is stored. You can
              initialize several tours with different names in the same page and
              application.</td>
              <td><code class="language-javascript">'tour'</code></td>
            </tr>
            <tr>
              <td>container</td>
              <td>String</td>
              <td>Appends the step popover to a specific element.<br>
              <em>See Usage section of
              <a href="http://twitter.github.io/bootstrap/javascript.html#popovers" target="_blank">Popover</a>.
              </em>
              </td>
              <td><code class="language-javascript">'body'</code></td>
            </tr>
            <tr>
              <td>keyboard</td>
              <td>Boolean</td>
              <td>This option set the left and right arrow navigation.</td>
              <td><code class="language-javascript">true</code></td>
            </tr>
            <tr>
              <td>storage</td>
              <td>Object</td>
              <td>The storage system you want to use. could be the objects window.localStorage,
              window.sessionStorage or your own object.<br>
              You can set this option as
              <code class="language-javascript">false</code> to disable storage
              persistence (the tour starts from beginning everytime the page is
              loaded).<br>
              <em>Read more about <a href="https://developer.mozilla.org/en-US/docs/Web/Guide/API/DOM/Storage" target="_blank">DOM Storage interfaces</a>.</em>
              </td>
              <td><code class="language-javascript">window.localStorage</code></td>
            </tr>
            <tr>
              <td>debug</td>
              <td>Boolean</td>
              <td>Set this option to true to have some useful informations printed in the
              console.</td>
              <td><code class="language-javascript">false</code></td>
            </tr>
            <tr>
              <td>backdrop</td>
              <td>Boolean</td>
              <td>Show a dark backdrop behind the popover and its element,
              highlighting the current step.</td>
              <td><code class="language-javascript">false</code></td>
            </tr>
            <tr>
              <td>redirect</td>
              <td>Boolean|Function</td>
              <td>Set a custom function to execute as redirect function.
              The default redirect relies on the traditional
              <code class="language-javascrip">document.location.href</code></td>
              <td><code class="language-javascript">true</code></td>
            </tr>
            <tr>
              <td>orphan</td>
              <td>Boolean</td>
              <td>Allow to show the step regardless whether its element is not set, is
              not present in the page or is hidden. The step is fixed
              positioned in the middle of the page.</td>
              <td><code class="language-javascript">false</code></td>
            </tr>
            <tr id="duration" class="success">
              <td>duration <span class="label label-success">NEW</span></td>
              <td>Boolean|String</td>
              <td>Set a expiration time for the steps. When the current step expires,
              the next step is automatically shown. See it as a sort of guided, automatized tour
              functionality. The value is specified in milliseconds</td>
              <td><code class="language-javascript">false</code></td>
            </tr>
            <tr>
              <td>basePath</td>
              <td>String</td>
              <td>Specify a default base path prepended to the
              <code class="language-javascript">path</code> option of every single
              step. Very useful if you need to reuse the same tour on different
              environments or sub-projects.</td>
              <td><code class="language-javascript">''</code></td>
            </tr>
            <tr>
              <td>template</td>
              <td>String|Function</td>
              <td>String or function that returns a string of the HTML template for
              the popovers. If you pass a Function, two parameters are available:
              <strong>i</strong> is the position of step in the tour and
              <strong>step</strong> is the an object that contains all the other step
              options.<br>
              From version 0.5, the navigation template is included inside the
              template so you can easily rewrite it. However, Bootstrap Tour maps the
              <em>previous</em>, <em>next</em> and <em>end</em> logics to the elements
              which have the related <code class="language-markup">data-role</code>
              attribute. Therefore, you can also have multiple elements with the same
              <code class="language-markup">data-role</code> attribute.
              </td>
              <td><pre class="language-javascript"><code>"&lt;div class='popover tour'&gt;
  &lt;div class='arrow'&gt;&lt;/div&gt;
  &lt;h3 class='popover-title'&gt;&lt;/h3&gt;
  &lt;div class='popover-content'&gt;&lt;/div&gt;
  &lt;div class='popover-navigation'&gt;
    &lt;button class='btn btn-default' data-role='prev'&gt;&laquo; Prev&lt;/button&gt;
    &lt;span data-role='separator'&gt;|&lt;/span&gt;
    &lt;button class='btn btn-default' data-role='next'&gt;Next &raquo;&lt;/button&gt;
    &lt;button class='btn btn-default' data-role='end'&gt;End tour&lt;/button&gt;
  &lt;/div&gt;
&lt;/div&gt;"</code></pre>
              </td>
              </p>
            <tr>
            <tr>
              <td>afterGetState, afterSetState, afterRemoveState</td>
              <td>Function</td>
              <td>You may want to do something right after Bootstrap Tour read, write or remove
              the state. Just pass functions to these.<br />
              Your functions can have two parameters:
              <ul class="unstyled">
                <li>
                  <strong>key</strong>
                  Contains the name of the state being saved. It can be
                  <code class="language-javascript">current_step</code> (for the state where the
                  latest step the visitor viewed is saved) or
                  <code class="language-javascript">end</code> (for the state which is saved when
                  the user complete the tour). Note that Bootstrap Tour prepends the key with
                  <code class="language-javascript">tour_</code> when saving the state.
                </li>
                <li>
                  <strong>value</strong>
                  The value of the state been saved. Can be the index of the
                  current step if the key is <code class="language-javascript">current_step</code>, or
                  <code class="language-javascript">yes</code> if the key is <code class="language-javascript">end</code>.
                </li>
              </ul>
              <p>A simple example if to send a post request to your server each
              time there is a change:</p>
<pre class="language-javascript"><code>var tour = new Tour({
  afterSetState: function(key, value) {
    $.post("/some/path", value);
  }
});</code></pre>
              </td>
              <td><code class="language-javascript">function(key, value) { }</code></td>
            </tr>
            <tr>
              <td>onStart</td>
              <td>Function</td>
              <td>Function to execute when the tour starts.</td>
              <td><code class="language-javascript">function(tour) { }</code></td>
            </tr>
            <tr>
              <td>onEnd</td>
              <td>Function</td>
              <td>Function to execute when the tour ends.</td>
              <td><code class="language-javascript">function(tour) { }</code></td>
            </tr>
            <tr>
              <td>onShow</td>
              <td>Function</td>
              <td>Function to execute right before each step is shown.</td>
              <td><code class="language-javascript">function(tour) { }</code></td>
            </tr>
            <tr>
              <td>onShown</td>
              <td>Function</td>
              <td>Function to execute right after each step is shown.</td>
              <td><code class="language-javascript">function(tour) { }</code></td>
            </tr>
            <tr>
              <td>onHide</td>
              <td>Function</td>
              <td>Function to execute right before each step is hidden.</td>
              <td><code class="language-javascript">function(tour) { }</code></td>
            </tr>
            <tr>
              <td>onHidden</td>
              <td>Function</td>
              <td>Function to execute right after each step is hidden.</td>
              <td><code class="language-javascript">function(tour) { }</code></td>
            </tr>
            <tr>
              <td>onNext</td>
              <td>Function</td>
              <td>Function to execute when next step is called.</td>
              <td><code class="language-javascript">function(tour) { }</code></td>
            </tr>
            <tr>
              <td>onPrev</td>
              <td>Function</td>
              <td>Function to execute when prev step is called.</td>
              <td><code class="language-javascript">function(tour) { }</code></td>
            </tr>
            <tr class="success">
              <td>onPause <span class="label label-success">NEW</span></td>
              <td>Function</td>
              <td>Function to execute when pause is called. The second argument refers to the
              remaining duration.</td>
              <td><code class="language-javascript">function(tour, duration) { }</code></td>
            </tr>
            <tr class="success">
              <td>onResume <span class="label label-success">NEW</span></td>
              <td>Function</td>
              <td>Function to execute when resume is called. The second argument refers to the
              remaining duration.</td>
              <td><code class="language-javascript">function(tour, duration) { }</code></td>
            </tr>
          </tbody>
        </table>
        <a id="step-options"></a>
        <h3>Step Options</h3>
<pre class="language-javascript"><code>tour.addStep({
  path: "",
  element: "",
  placement: "right",
  title: "",
  content: "",
  next: 0,
  prev: 0,
  animation: true,
  container: "body",
  backdrop: false,
  redirect: true,
  reflex: false,
  orphan: false,
  template: "",
  onShow: function(tour) {},
  onShown: function(tour) {},
  onHide: function(tour) {},
  onHidden: function(tour) {},
  onNext: function(tour) {},
  onPrev: function(tour) {},
  onPause: function(tour) {},
  onResume: function(tour) {}
});</code></pre>
        <table class="table  table-bordered table-striped">
          <thead>
            <tr>
              <th>Name</th>
              <th>Type</th>
              <th>Description</th>
              <th>Default</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>path</td>
              <td>String</td>
              <td>Path to the page on which the step should be shown. This
              allows you to build tours that span several pages!</td>
              <td><code class="language-javascript">''</code></td>
            </tr>
            <tr>
              <td>element</td>
              <td>String (jQuery selector)</td>
              <td>HTML element on which the step popover should be shown.<br />
              <em>If orphan is false, this option is required.</em></td>
              <td><code class="language-javascript">''</code></td>
            </tr>
            <tr>
              <td>placement</td>
              <td>String|Function</td>
              <td>How to position the popover. Possible choices:
              <code class="language-javascript">'top'</code>,
              <code class="language-javascript">'bottom'</code>,
              <code class="language-javascript">'left'</code>,
              <code class="language-javascript">'right'</code>.
              </td>
              <td><code class="language-javascript">'right'</code></td>
            </tr>
            <tr>
              <td>title</td>
              <td>String|Function</td>
              <td>Step title</td>
              <td><code class="language-javascript">''</code></td>
            </tr>
            <tr>
              <td>content</td>
              <td>String|Function</td>
              <td>Step content</td>
              <td><code class="language-javascript">''</code></td>
            </tr>
            <tr>
              <td>next</td>
              <td>Integer</td>
              <td>Index of the step to show after this one, starting from
              <code class="language-javascript">0</code> for the first step of the
              tour. <code class="language-javascript">-1</code> to not show the link
              to next step. By default, the next step (in the order you added
              them) will be shown.<br />
              <em>This option should be used in conjunction with
              <code class="language-javascript">prev</code>.</em></td>
              <td><code class="language-javascript">0</code></td>
            </tr>
            <tr>
              <td>prev</td>
              <td>Integer</td>
              <td>Index of the step to show before this one, starting from
              <code class="language-javascript">0</code> for the first step of the
              tour. <code class="language-javascript">-1</code> to not show the link
              to previous step. By default, the previous step (in the order you added
              them) will be shown.<br />
              <em>This option should be used in conjunction with
              <code class="language-javascript">next</code>.</em></td>
              <td><code class="language-javascript">0</code></td>
            </tr>
            <tr>
              <td>animation</td>
              <td>Boolean</td>
              <td>Apply a css fade transition to the tooltip.</td>
              <td><code class="language-javascript">true</code></td>
            </tr>
            <tr>
              <td>container</td>
              <td>String (jQuery selector)</td>
              <td>Attachment of popover. Pass an element to append the popover
              to. By default the popover is appended after the 'element' above.
              This option is particularly helpful for Internet Explorer.</td>
              <td><code class="language-javascript">'body'</code></td>
            </tr>
            <tr>
              <td>backdrop</td>
              <td>Boolean</td>
              <td>Show a dark backdrop behind the popover and its element,
              highlighting the current step.</td>
              <td><code class="language-javascript">false</code></td>
            </tr>
            <tr>
              <td>redirect</td>
              <td>Boolean|Function</td>
              <td>Set a custom function to execute as redirect function.
              The default redirect relies on the traditional
              <code class="language-javascrip">document.location.href</code></td>
              <td><code class="language-javascript">true</code></td>
            </tr>
            <tr>
              <td>reflex</td>
              <td>Boolean</td>
              <td>Enable the reflex mode: you can click on the element for
              continue the tour.</td>
              <td><code class="language-javascript">false</code></td>
            </tr>
            <tr>
              <td>orphan</td>
              <td>Boolean</td>
              <td>Allow to show the step regardless whether its element is not set, is
              not present in the page or is hidden. The step is fixed
              positioned in the middle of the page.</td>
              <td><code class="language-javascript">false</code></td>
            </tr>
            <tr class="success">
              <td>duration <span class="label label-success">NEW</span></td>
              <td>Boolean|String</td>
              <td>Set a expiration time for the stes. When the step expires,
              the next step is automatically shown. See it as a sort of guided, automatized tour
              functionality. The value is specified in milliseconds</td>
              <td><code class="language-javascript">false</code></td>
            </tr>
            <tr>
              <td>template</td>
              <td>String|Function</td>
              <td>String or function that returns a string of the HTML template for
              the popovers. If you pass a Function, two parameters are available:
              <strong>i</strong> is the position of step in the tour and
              <strong>step</strong> is the an object that contains all the other step
              options.<br>
              From version 0.5, the navigation template is included inside the
              template so you can easily rewrite it. However, Bootstrap Tour maps the
              <em>previous</em>, <em>next</em> and <em>end</em> logics to the elements
              which have the related <code class="language-markup">data-role</code>
              attribute. Therefore, you can also have multiple elements with the same
              <code class="language-markup">data-role</code> attribute.
              </td>
              <td><pre class="language-javascript"><code>"&lt;div class='popover tour'&gt;
  &lt;div class='arrow'&gt;&lt;/div&gt;
  &lt;h3 class='popover-title'&gt;&lt;/h3&gt;
  &lt;div class='popover-content'&gt;&lt;/div&gt;
  &lt;div class='popover-navigation'&gt;
    &lt;button class='btn btn-default' data-role='prev'&gt;&laquo; Prev&lt;/button&gt;
    &lt;span data-role='separator'&gt;|&lt;/span&gt;
    &lt;button class='btn btn-default' data-role='next'&gt;Next &raquo;&lt;/button&gt;
    &lt;button class='btn btn-default' data-role='end'&gt;End tour&lt;/button&gt;
  &lt;/div&gt;
&lt;/div&gt;"</code></pre>
              </td>
            </tr>
            <tr>
              <td>onShow</td>
              <td>Function</td>
              <td>Function to execute right before the step is shown. It overrides the
              global <code class="language-javascript">onShow</code> option.</td>
              <td><code class="language-javascript">function(tour) { }</code></td>
            </tr>
            <tr>
              <td>onShown</td>
              <td>Function</td>
              <td>Function to execute right after the step is shown. It overrides the
              global <code class="language-javascript">onShown</code> option.</td>
              <td><code class="language-javascript">function(tour) { }</code></td>
            </tr>
            <tr>
              <td>onHide</td>
              <td>Function</td>
              <td>Function to execute right before the step is hidden. It overrides
              the global <code class="language-javascript">onHide</code> option.</td>
              <td><code class="language-javascript">function(tour) { }</code></td>
            </tr>
            <tr>
              <td>onHidden</td>
              <td>Function</td>
              <td>Function to execute right after the step is hidden. It overrides the
              global <code class="language-javascript">onHidden</code> option.</td>
              <td><code class="language-javascript">function(tour) { }</code></td>
            </tr>
            <tr>
              <td>onNext</td>
              <td>Function</td>
              <td>Function to execute when next step is called. It overrides the
              global <code class="language-javascript">onNext</code> option.</td>
              <td><code class="language-javascript">function(tour) { }</code></td>
            </tr>
            <tr>
              <td>onPrev</td>
              <td>Function</td>
              <td>Function to execute when prev step is called. It overrides the global
              <code class="language-javascript">onPrev</code> option.</td>
              <td><code class="language-javascript">function(tour) { }</code></td>
            </tr>
            <tr class="success">
              <td>onPause <span class="label label-success">NEW</span></td>
              <td>Function</td>
              <td>Function to execute when pause is called. The second argument refers to the
              remaining duration. It overrides the global the
              <code class="language-javascript">onPause</code> option</td>
              <td><code class="language-javascript">function(tour, duration) { }</code></td>
            </tr>
            <tr class="success">
              <td>onResume <span class="label label-success">NEW</span></td>
              <td>Function</td>
              <td>Function to execute when resume is called. The second argument refers to the
              remaining duration. It overrides the global
              <code class="language-javascript">onResume</code> option</td>
              <td><code class="language-javascript">function(tour, duration) { }</code></td>
            </tr>
          </tbody>
        </table>
      </div>
    </section>
    <section id="methods">
      <div class="container">
        <div class="page-header">
          <h2>Methods <small>Always in control</small></h2>
        </div>
        <!--<p>If, for some reasons, you want to force the tour to be
        displayed, pass <code class="language-javascript">true</code> to the <code class="language-javascript">start()</code>
        method.</p>-->
        <table class="table  table-bordered table-striped">
          <thead>
            <tr>
              <th>Name</th>
              <th>Description</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>init()</td>
              <td>Initialize the tour. You must do it before calling start.</td>
            </tr>
            <tr>
              <td>start(<code class="language-javascript">true</code>)</td>
              <td>Start the tour. Pass <code class="language-javascript">true</code> to force the start.</td>
            </tr>
            <tr>
              <td>restart()</td>
              <td>Restart the tour after it ended.</td>
            </tr>
            <tr>
              <td>end()</td>
              <td>End the tour prematurely.</td>
            </tr>
            <tr>
              <td>next()</td>
              <td>Skip to the next step.</td>
            </tr>
            <tr>
              <td>prev()</td>
              <td>Go back to the previous step.</td>
            </tr>
            <tr class="warning">
              <td>goTo(<code class="language-javascript">i</code>)
              <span class="label label-warning">UPDATED</span></td>
              <td>Skip to a specific step. Pass <code class="language-javascript">i</code> as the
              position of the step in the tour, starting from 0 for the first step.<br>
              <em>From version 0.7.0, the method has been renamed since some Javascript compilers
              are confused by the property name <strong>goto</strong>, which is a reserved word)
              </em>
              </td>
            </tr>
            <tr>
              <td>pause()</td>
              <td>Pause the duration timer. It works only if tour or current stap has duration.</td>
            </tr>
            <tr>
              <td>resume()</td>
              <td>Resume the duration timer. It works only if tour or current stap has duration.</td>
            </tr>
            <tr>
              <td>ended()</td>
              <td>Verify if the tour ended. Returns boolean.</td>
            </tr>
          </tbody>
        </table>
      </div>
    </section>
    <section id="contributing">
      <div class="container">
        <div class="page-header">
          <h2>Contributing <small>Do you think we need help? You are totally right</small></h2>
        </div>
        <p>Without your <a href="https://github.com/sorich87/bootstrap-tour/issues">bug
        reports</a> and <a href="https://github.com/sorich87/bootstrap-tour/pulls">pull
        requests</a>, we are nothing. Make this plugin better!</p>
        <h3>Grunt Usage</h3>
        <div id="grunt-usage" class="accordion">
          <div class="panel panel-default">
            <div class="panel-heading">
              <h4 class="panel-title">
                <a class="accordion-toggle" data-toggle="collapse" data-parent="#grunt-usage" href="#install-dependencies">Install Dependencies</a>
              </h4>
            </div>
              <div id="install-dependencies" class="panel-collapse collapse in">
                <div class="panel-body">
                  <p>Files to be developed inside the project are located under
                  <code>/src/</code>.</p>
                  <p>Compiled sources are then automatically put under
                  <code>/build/</code> (and <code>/test/</code>).</p>
              </div>
            </div>
          </div>
          <div class="panel panel-default">
            <div class="panel-heading">
              <h4 class="panel-title">
                <a class="accordion-toggle" data-toggle="collapse" data-parent="#grunt-usage" href="#run-tasks">Run main tasks (check <code>Gruntfile.coffee</code> for more infos)</a>
              </h4>
            </div>
            <div id="run-tasks" class="accordion-body collapse">
              <div class="panel-body">
<pre class="language-javascript"><code>// Start a server and run the demo page
grunt
grunt run
// Compile all sources
grunt build
// Compile all sources and run the tests
grunt test
// Automatically release a new version (see below for more details)
grunt release</code></pre>
              </div>
            </div>
          </div>
          <div class="panel panel-default">
            <div class="panel-heading">
              <h4 class="panel-title">
                <a class="accordion-toggle" data-toggle="collapse" data-parent="#grunt-usage" href="#releasing">Automatically releasing</a>
              </h4>
            </div>
            <div id="releasing" class="accordion-body collapse">
              <div class="panel-body">
                <p>Releasing a new version is completely automated using the Grunt task <code class="language-bash">grunt release</code>.</p>
<pre class="language-javascript"><code>grunt release // patch release
grunt release:minor // minor release
grunt release:major // major release</code></pre>
              </div>
            </div>
          </div>
          <div class="panel panel-default">
              <div class="panel-heading">
                <h4 class="panel-title">
                  <a class="accordion-toggle" data-toggle="collapse" data-parent="#grunt-usage" href="#live-reload">Live reload</a>
                </h4>
              </div>
              <div id="live-reload" class="accordion-body collapse">
                <div class="panel-body">
                  <p>Running <code class="language-bash">grunt run</code> will start a small server on port <code class="language-bash">3000</code> and opens the browser to the webpage. It will also start watching for changes in the <code class="language-bash">index.coffee</code> which will trigger then live reloading of the page in the browser.</p>
                </div>
              </div>
          </div>
          <div class="panel panel-default">
            <div class="panel-heading">
              <h4 class="panel-title">
                <a class="accordion-toggle" data-toggle="collapse" data-parent="#grunt-usage" href="#test">Run the tests</a>
              </h4>
            </div>
            <div id="test" class="accordion-body collapse">
              <div class="panel-body">
                <p>Tests are written using <a href="http://pivotal.github.io/jasmine/" target="_blank">Jasmine</a> and they run headlessly through <a href="http://phantomjs.org/" target="_blank">PhantomJS</a>.<br>
                Simply run <code class="language-bash">grunt test</code> or watch them with <code class="language-bash">grunt watch:test</code> (this will execute them automatically every time you change the specs).</p>
                <p>You can also open the <code>_SpecRunner.html</code> (generated after you run <code class="language-bash">grunt test</code>) to run the tests in the browser.</p>
              </div>
            </div>
          </div>
        </div>
        <h3>Team</h3>
        <div class="row">
          <div class="col-sm-4">
            <img src="" class="gravatar img-circle" data-email="<EMAIL>"> Ulrich Sossou
          </div>
          <div class="col-sm-4">
            <img src="" class="gravatar img-circle" data-email="<EMAIL>"> Emanuele Marchi
          </div>
          <div class="col-sm-4">
            <img src="" class="gravatar img-circle" data-email="<EMAIL>"> Luca Molinari
          </div>
      </div>
    </section>
    <section id="license">
      <div class="container">
        <div class="page-header">
          <h2>License</h2>
        </div>
        <p>Code licensed under the <a href="http://www.apache.org/licenses/LICENSE-2.0" target="_blank">Apache License v2.0</a>.<br>
        Documentation licensed under <a href="http://creativecommons.org/licenses/by/3.0/">CC BY 3.0</a>.<br>
        Well, the same licenses as Bootstrap. We are lazy! ;)</p>
      </div>
    </section>
    <script src="assets/vendor/jquery.js"></script>
    <script src="assets/vendor/bootstrap.js"></script>
    <script src="assets/vendor/prism.js"></script>
    <script src="assets/vendor/jquery.smoothscroll.js"></script>
    <script src="assets/vendor/md5.js"></script>
    <script src="assets/js/bootstrap-tour.js"></script>
    <script src="assets/js/index.js"></script>
  </body>
</html>