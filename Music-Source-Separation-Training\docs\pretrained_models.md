## Pre-trained models 

If you trained some good models, please, share them. You can post config and model weights [in this issue](https://github.com/ZFTurbo/Music-Source-Separation-Training/issues/1).

### Vocal models

|                                    Model Type                                    | Instruments |   Metrics (SDR)   | Config | Checkpoint |
|:--------------------------------------------------------------------------------:|:-------------:|:-----------------:|:-----:|:-----:|
|                                      MDX23C                                      | vocals / other | SDR vocals: 10.17 | [Config](https://github.com/ZFTurbo/Music-Source-Separation-Training/releases/download/v1.0.0/config_vocals_mdx23c.yaml) | [Weights](https://github.com/ZFTurbo/Music-Source-Separation-Training/releases/download/v1.0.0/model_vocals_mdx23c_sdr_10.17.ckpt) |
|                           HTDemucs4 (MVSep finetuned)                            | vocals / other | SDR vocals: 8.78  | [Config](https://raw.githubusercontent.com/ZFTurbo/Music-Source-Separation-Training/main/configs/config_vocals_htdemucs.yaml) | [Weights](https://github.com/ZFTurbo/Music-Source-Separation-Training/releases/download/v1.0.0/model_vocals_htdemucs_sdr_8.78.ckpt) |
|                             Segm Models (VitLarge23)                             | vocals / other | SDR vocals: 9.77  | [Config](https://github.com/ZFTurbo/Music-Source-Separation-Training/releases/download/v1.0.0/config_vocals_segm_models.yaml) | [Weights](https://github.com/ZFTurbo/Music-Source-Separation-Training/releases/download/v1.0.0/model_vocals_segm_models_sdr_9.77.ckpt) |
|                                   Swin Upernet                                   | vocals / other | SDR vocals: 7.57  | [Config](https://github.com/ZFTurbo/Music-Source-Separation-Training/releases/download/v1.0.2/config_vocals_swin_upernet.yaml) | [Weights](https://github.com/ZFTurbo/Music-Source-Separation-Training/releases/download/v1.0.2/model_swin_upernet_ep_56_sdr_10.6703.ckpt) |
|         BS Roformer ([viperx](https://github.com/playdasegunda) edition)         | vocals / other | SDR vocals: 10.87 | [Config](https://raw.githubusercontent.com/ZFTurbo/Music-Source-Separation-Training/main/configs/viperx/model_bs_roformer_ep_317_sdr_12.9755.yaml) | [Weights](https://github.com/TRvlvr/model_repo/releases/download/all_public_uvr_models/model_bs_roformer_ep_317_sdr_12.9755.ckpt) |
|      MelBand Roformer ([viperx](https://github.com/playdasegunda) edition)      | vocals / other | SDR vocals: 9.67  | [Config](https://raw.githubusercontent.com/ZFTurbo/Music-Source-Separation-Training/main/configs/viperx/model_mel_band_roformer_ep_3005_sdr_11.4360.yaml) | [Weights](https://github.com/TRvlvr/model_repo/releases/download/all_public_uvr_models/model_mel_band_roformer_ep_3005_sdr_11.4360.ckpt) |
| MelBand Roformer ([KimberleyJensen](https://github.com/KimberleyJensen/) edition) | vocals / other | SDR vocals: 10.98 | [Config](https://raw.githubusercontent.com/ZFTurbo/Music-Source-Separation-Training/main/configs/KimberleyJensen/config_vocals_mel_band_roformer_kj.yaml) | [Weights](https://huggingface.co/KimberleyJSN/melbandroformer/resolve/main/MelBandRoformer.ckpt) |

**Note**: Metrics measured on [Multisong Dataset](https://mvsep.com/en/quality_checker).

### Single stem models

|                                                  Model Type                                                   | Instruments |  Metrics (SDR)   | Config |                                                                       Checkpoint                                                                        |
|:-------------------------------------------------------------------------------------------------------------:|:-----------:|:----------------:|:-----:|:-------------------------------------------------------------------------------------------------------------------------------------------------------:|
|                                              HTDemucs4 FT Drums                                               |    drums    | SDR drums: 11.13 | [Config](https://raw.githubusercontent.com/ZFTurbo/Music-Source-Separation-Training/main/configs/config_musdb18_htdemucs.yaml) |                                [Weights](https://dl.fbaipublicfiles.com/demucs/hybrid_transformer/f7e0c4bc-ba3fe64a.th)                                 |
|                                               HTDemucs4 FT Bass                                               |    bass     | SDR bass: 11.96  | [Config](https://raw.githubusercontent.com/ZFTurbo/Music-Source-Separation-Training/main/configs/config_musdb18_htdemucs.yaml) |                                [Weights](https://dl.fbaipublicfiles.com/demucs/hybrid_transformer/d12395a8-e57c48e6.th)                                 |
|                                              HTDemucs4 FT Other                                               |    other    | SDR other: 5.85  | [Config](https://raw.githubusercontent.com/ZFTurbo/Music-Source-Separation-Training/main/configs/config_musdb18_htdemucs.yaml) |                                [Weights](https://dl.fbaipublicfiles.com/demucs/hybrid_transformer/92cfc3b6-ef3bcb9c.th)                                 |
|                                   HTDemucs4 FT Vocals (Official repository)                                   |   vocals    | SDR vocals: 8.38 | [Config](https://raw.githubusercontent.com/ZFTurbo/Music-Source-Separation-Training/main/configs/config_musdb18_htdemucs.yaml) |                                [Weights](https://dl.fbaipublicfiles.com/demucs/hybrid_transformer/04573f0d-f3cf25b2.th)                                 |
|                       BS Roformer ([viperx](https://github.com/playdasegunda) edition)                        |    other    | SDR other: 6.85  | [Config](https://raw.githubusercontent.com/ZFTurbo/Music-Source-Separation-Training/main/configs/viperx/model_bs_roformer_ep_937_sdr_10.5309.yaml) |            [Weights](https://github.com/TRvlvr/model_repo/releases/download/all_public_uvr_models/model_bs_roformer_ep_937_sdr_10.5309.ckpt)            |
| MelBand Roformer ([aufr33](https://github.com/aufr33) and [viperx](https://github.com/playdasegunda) edition) |    crowd    | SDR crowd: 5.99  | [Config](https://github.com/ZFTurbo/Music-Source-Separation-Training/releases/download/v.1.0.4/model_mel_band_roformer_crowd.yaml) | [Weights](https://github.com/ZFTurbo/Music-Source-Separation-Training/releases/download/v.1.0.4/mel_band_roformer_crowd_aufr33_viperx_sdr_8.7144.ckpt)  |
|                        MelBand Roformer ([anvuew](https://github.com/anvuew) edition)                         |  dereverb   |       ---        | [Config](https://huggingface.co/anvuew/dereverb_mel_band_roformer/resolve/main/dereverb_mel_band_roformer_anvuew.yaml) |           [Weights](https://huggingface.co/anvuew/dereverb_mel_band_roformer/resolve/main/dereverb_mel_band_roformer_anvuew_sdr_19.1729.ckpt)           |
|                       MelBand Roformer Denoise (by [aufr33](https://github.com/aufr33))                       |   denoise   |       ---        | [Config](https://github.com/ZFTurbo/Music-Source-Separation-Training/releases/download/v.1.0.7/model_mel_band_roformer_denoise.yaml) |   [Weights](https://github.com/ZFTurbo/Music-Source-Separation-Training/releases/download/v.1.0.7/denoise_mel_band_roformer_aufr33_sdr_27.9959.ckpt)    |
|                 MelBand Roformer Denoise Aggressive (by [aufr33](https://github.com/aufr33))                  |   denoise   |       ---        | [Config](https://github.com/ZFTurbo/Music-Source-Separation-Training/releases/download/v.1.0.7/model_mel_band_roformer_denoise.yaml) | [Weights](https://github.com/ZFTurbo/Music-Source-Separation-Training/releases/download/v.1.0.7/denoise_mel_band_roformer_aufr33_aggr_sdr_27.9768.ckpt) |
|                   Apollo LQ MP3 restoration (by [JusperLee](https://github.com/JusperLee))                    |  restored   |       ---        | [Config](https://github.com/ZFTurbo/Music-Source-Separation-Training/blob/main/configs/config_apollo.yaml) |                                    [Weights](https://huggingface.co/JusperLee/Apollo/resolve/main/pytorch_model.bin)                                    |
|              MelBand Roformer Aspiration (by [SUC-DriverOld](https://github.com/SUC-DriverOld))               | aspiration  |    SDR: 9.85     | [Config](https://huggingface.co/Sucial/Aspiration_Mel_Band_Roformer/blob/main/config_aspiration_mel_band_roformer.yaml) |              [Weights](https://huggingface.co/Sucial/Aspiration_Mel_Band_Roformer/blob/main/aspiration_mel_band_roformer_sdr_18.9845.ckpt)              |
|                MDX23C Phantom Centre extraction (by [wesleyr36](https://github.com/wesleyr36))                | similarity  |  L1Freq: 72.23   | [Config](https://github.com/ZFTurbo/Music-Source-Separation-Training/releases/download/v1.0.10/config_mdx23c_similarity.yaml) |        [Weights](https://github.com/ZFTurbo/Music-Source-Separation-Training/releases/download/v1.0.10/model_mdx23c_ep_271_l1_freq_72.2383.ckpt)        |
|        MelBand Roformer Vocals DeReverb/DeEcho (by [SUC-DriverOld](https://github.com/SUC-DriverOld))         | dry |    SDR: 10.01    |  [Config](https://huggingface.co/Sucial/Dereverb-Echo_Mel_Band_Roformer/resolve/main/config_dereverb-echo_mel_band_roformer.yaml) |                                                                       [Weights](https://huggingface.co/Sucial/Dereverb-Echo_Mel_Band_Roformer/resolve/main/dereverb-echo_mel_band_roformer_sdr_10.0169.ckpt)                                                                       |

**Note**: All HTDemucs4 FT models output 4 stems, but quality is best only on target stem (all other stems are dummy).

### Multi-stem models

|                                             Model Type                                              |                  Instruments                   |                                                                   Metrics (SDR)                                                                    | Config | Checkpoint |
|:---------------------------------------------------------------------------------------------------:|:----------------------------------------------:|:--------------------------------------------------------------------------------------------------------------------------------------------------:|:-----:|:-----:|
|                                             BandIt Plus                                             |            speech / music / effects            |                                           DnR test avg: 11.50 (speech: 15.64, music: 9.18 effects: 9.69)                                           | [Config](https://github.com/ZFTurbo/Music-Source-Separation-Training/releases/download/v.1.0.3/config_dnr_bandit_bsrnn_multi_mus64.yaml) | [Weights](https://github.com/ZFTurbo/Music-Source-Separation-Training/releases/download/v.1.0.3/model_bandit_plus_dnr_sdr_11.47.chpt) |
|                                              HTDemucs4                                              |         bass / drums / vocals / other          |                                      Multisong avg: 9.16 (bass: 11.76, drums: 10.88 vocals: 8.24 other: 5.74)                                      | [Config](https://raw.githubusercontent.com/ZFTurbo/Music-Source-Separation-Training/main/configs/config_musdb18_htdemucs.yaml) | [Weights](https://dl.fbaipublicfiles.com/demucs/hybrid_transformer/955717e8-8726e21a.th) |
|                                         HTDemucs4 (6 stems)                                         | bass / drums / vocals / other / piano / guitar |                                Multisong (bass: 11.22, drums: 10.22 vocals: 8.05 other: --- piano: --- guitar: ---)                                | [Config](https://raw.githubusercontent.com/ZFTurbo/Music-Source-Separation-Training/main/configs/config_htdemucs_6stems.yaml) | [Weights](https://dl.fbaipublicfiles.com/demucs/hybrid_transformer/5c90dfd2-34c22ccb.th) |
|                                             Demucs3 mmi                                             |         bass / drums / vocals / other          |                                      Multisong avg: 8.88 (bass: 11.17, drums: 10.70 vocals: 8.22 other: 5.42)                                      | [Config](https://raw.githubusercontent.com/ZFTurbo/Music-Source-Separation-Training/main/configs/config_musdb18_demucs3_mmi.yaml) | [Weights](https://dl.fbaipublicfiles.com/demucs/hybrid_transformer/75fc33f5-1941ce65.th) |
|                      DrumSep htdemucs (by [inagoy](https://github.com/inagoy))                      |         kick / snare / cymbals / toms          |                                                                        ---                                                                         | [Config](https://raw.githubusercontent.com/ZFTurbo/Music-Source-Separation-Training/main/configs/config_drumsep.yaml) | [Weights](https://github.com/ZFTurbo/Music-Source-Separation-Training/releases/download/v1.0.5/model_drumsep.th) |
| DrumSep mdx23c (by [aufr33](https://github.com/aufr33) and [jarredou](https://github.com/jarredou)) |    kick / snare / toms / hh / ride / crash     |                                                                        ---                                                                         | [Config](https://github.com/jarredou/models/releases/download/aufr33-jarredou_MDX23C_DrumSep_model_v0.1/aufr33-jarredou_DrumSep_model_mdx23c_ep_141_sdr_10.8059.yaml) | [Weights](https://github.com/jarredou/models/releases/download/aufr33-jarredou_MDX23C_DrumSep_model_v0.1/aufr33-jarredou_DrumSep_model_mdx23c_ep_141_sdr_10.8059.ckpt) |

### Multi-stem models (MUSDB18HQ)

* Models in this list were trained only on MUSDB18HQ dataset (100 songs train data). These weights are useful for fine-tuning.
* Instruments: bass / drums / vocals / other

|                          Model Type                          |                                                                     Metrics (SDR)                                                                      | Config | Checkpoint |
|:------------------------------------------------------------:|:------------------------------------------------------------------------------------------------------------------------------------------------------:|:-----:|:-----:|
|                            MDX23C                            |  MUSDB test avg: 7.15 (bass: 5.77, drums: 7.93 vocals: 9.23 other: 5.68) <br> Multisong avg: 7.02 (bass: 8.40, drums: 7.73 vocals: 7.36 other: 4.57)   | [Config](https://github.com/ZFTurbo/Music-Source-Separation-Training/releases/download/v1.0.1/config_musdb18_mdx23c.yaml) | [Weights](https://github.com/ZFTurbo/Music-Source-Separation-Training/releases/download/v1.0.1/model_mdx23c_ep_168_sdr_7.0207.ckpt) |
|                         TS BS Mamba2                         |   MUSDB test avg: 6.87 (bass: 5.82, drums: 8.14 vocals: 8.35 other: 5.16)<br> Multisong avg: 6.66 (bass: 7.87, drums: 7.92 vocals: 7.01 other: 3.85)   | [Config](https://github.com/ZFTurbo/Music-Source-Separation-Training/releases/download/v1.0.9/config_musdb18_bs_mamba2.yaml) | [Weights](https://github.com/ZFTurbo/Music-Source-Separation-Training/releases/download/v1.0.9/model_bs_mamba2_ep_11_sdr_6.8723.ckpt) |
|    SCNet (by [starrytong](https://github.com/starrytong))    | MUSDB test avg: 9.03 (bass: 8.89, drums: 10.44 vocals: 9.90 other: 6.89)<br> Multisong avg: 8.87 (bass: 11.07, drums: 10.79 vocals: 8.27 other: 5.34)  | [Config](https://github.com/ZFTurbo/Music-Source-Separation-Training/releases/download/v.1.0.6/config_musdb18_scnet.yaml) | [Weights](https://github.com/ZFTurbo/Music-Source-Separation-Training/releases/download/v.1.0.6/scnet_checkpoint_musdb18.ckpt) |
|              SCNet Tran (analog of small SCNet)              | MUSDB test avg: 8.92 (bass: 8.07, drums: 10.81 vocals: 9.97 other: 6.84)<br> Multisong avg: 8.97 (bass: 10.99, drums: 10.87 vocals: 8.42 other: 5.63)  | [Config](https://github.com/ZFTurbo/Music-Source-Separation-Training/releases/download/v1.0.14/config_musdb18_scnet_tran.yaml) | [Weights](https://github.com/ZFTurbo/Music-Source-Separation-Training/releases/download/v1.0.14/model_scnet_tran_sdr_8.9272.ckpt) |
|                         SCNet Large                          | MUSDB test avg: 9.32 (bass: 8.63, drums: 10.89 vocals: 10.69 other: 7.06)<br> Multisong avg: 9.19 (bass: 11.15, drums: 11.04 vocals: 8.94 other: 5.62) | [Config](https://github.com/ZFTurbo/Music-Source-Separation-Training/releases/download/v1.0.8/config_musdb18_scnet_large.yaml) | [Weights](https://github.com/ZFTurbo/Music-Source-Separation-Training/releases/download/v1.0.8/model_scnet_sdr_9.3244.ckpt) |
| SCNet Large (by [starrytong](https://github.com/starrytong)) | MUSDB test avg: 9.70 (bass: 9.38, drums: 11.15 vocals: 10.94 other: 7.31)<br> Multisong avg: 9.28 (bass: 11.27, drums: 11.23 vocals: 9.05 other: 5.57) | [Config](https://github.com/ZFTurbo/Music-Source-Separation-Training/releases/download/v1.0.9/config_musdb18_scnet_large_starrytong.yaml) | [Weights](https://github.com/ZFTurbo/Music-Source-Separation-Training/releases/download/v1.0.9/SCNet-large_starrytong_fixed.ckpt) |
|                           SCNet XL                           | MUSDB test avg: 9.80 (bass: 9.23, drums: 11.51 vocals: 11.05 other: 7.41)<br> Multisong avg: 9.72 (bass: 11.87, drums: 11.49 vocals: 9.32 other: 6.19) | [Config](https://github.com/ZFTurbo/Music-Source-Separation-Training/releases/download/v1.0.13/config_musdb18_scnet_xl.yaml) | [Weights](https://github.com/ZFTurbo/Music-Source-Separation-Training/releases/download/v1.0.13/model_scnet_ep_54_sdr_9.8051.ckpt) |
|                         BS Roformer                          | MUSDB test avg: 9.65 (bass: 8.48, drums: 11.61 vocals: 11.08 other: 7.44)<br> Multisong avg: 9.38 (bass: 11.08, drums: 11.29 vocals: 9.19 other: 5.96) | [Config](https://github.com/ZFTurbo/Music-Source-Separation-Training/releases/download/v1.0.12/config_bs_roformer_384_8_2_485100.yaml) | [Weights](https://github.com/ZFTurbo/Music-Source-Separation-Training/releases/download/v1.0.12/model_bs_roformer_ep_17_sdr_9.6568.ckpt) |

### MelRoformer models

[Table of Mel Band Roformers with different paramers](mel_roformer_experiments.md)