"""
积分操作服务 - 优化版本
处理所有积分相关的业务逻辑
"""

import asyncio
import json
import threading
import time
from typing import Optional, Dict, Any, List
from enum import Enum

from func.tools.singleton_mode import singleton
from func.score.score_db import ScoreDB
from func.score.score_config import ScoreSystemConfig, ScoreOperationType, ScoreRankConfig
from func.log.default_log import DefaultLog

# 可选导入，避免依赖问题
try:
    from func.tools.common_websocket import CommonWebsocket
    WEBSOCKET_AVAILABLE = True
except ImportError:
    WEBSOCKET_AVAILABLE = False
    print("Warning: CommonWebsocket not available, broadcast功能将被禁用")

try:
    from func.tts.tts_core import TTsCore
    TTS_AVAILABLE = True
except ImportError:
    TTS_AVAILABLE = False
    print("Warning: TTsCore not available, 语音播报功能将被禁用")

try:
    from func.gobal.data import CommonData
    COMMON_DATA_AVAILABLE = True
except ImportError:
    COMMON_DATA_AVAILABLE = False
    print("Warning: CommonData not available, 部分功能可能受限")

@singleton
class OperScore:
    """积分操作服务类"""
    
    def __init__(self):
        """初始化积分操作服务"""
        print("OperScore 初始化开始...")
        
        # 核心依赖（必需）
        self.scoreDB = ScoreDB()
        self.log = DefaultLog().getLogger()
        
        # 可选依赖（优雅降级）
        self.commonWebsocket = None
        self.ttsCore = None
        self.commonData = None
        
        if WEBSOCKET_AVAILABLE:
            try:
                self.commonWebsocket = CommonWebsocket()
                self.log.info("WebSocket组件初始化成功")
            except Exception as e:
                self.log.warning(f"WebSocket组件初始化失败: {e}")
        
        if TTS_AVAILABLE:
            try:
                self.ttsCore = TTsCore()
                self.log.info("TTS组件初始化成功")
            except Exception as e:
                self.log.warning(f"TTS组件初始化失败: {e}")
        
        if COMMON_DATA_AVAILABLE:
            try:
                self.commonData = CommonData()
                self.log.info("CommonData组件初始化成功")
            except Exception as e:
                self.log.warning(f"CommonData组件初始化失败: {e}")
        
        # 用户每日积分统计缓存（生产环境建议使用Redis）
        self._daily_score_cache = {}
        
        # 初始化完成
        self.log.info("OperScore 初始化完成")

    def recharge_score(self, openId: str, score: int, oper: str = "充值") -> Optional[int]:
        """
        充值积分
        :param openId: 用户开放平台id
        :param score: 积分数量（可以是负数表示扣减）
        :param oper: 操作类型
        :return: 操作后的总积分，失败返回None
        """
        if not self._validate_score_operation(openId, score):
            return None
            
        try:
            # 查找用户信息
            user_info = self.scoreDB.get_score(openId)
            if not user_info:
                self.log.error(f"用户不存在，无法操作积分: {openId}")
                return None
            
            # 检查扣减积分时是否有足够的积分
            current_score = user_info.get('score', 0)
            if score < 0 and current_score + score < 0:
                self.log.warning(f"用户积分不足，无法扣减: {openId}, 当前:{current_score}, 扣减:{abs(score)}")
                return None
            
            # 执行积分操作
            success = self.scoreDB.oper_score(openId, user_info['userName'], score, oper)
            
            if success:
                new_score = current_score + score
                self.log.info(f"用户{user_info['userName']}积分操作成功，当前积分：{new_score}")
                
                # 广播积分变更消息
                self._broadcast_score_change(openId, user_info['userName'], score, oper, user_info.get('userface', ''))
                
                return new_score
            else:
                self.log.error(f"积分操作失败: {openId}")
                return None
                
        except Exception as e:
            self.log.error(f"充值积分异常：{str(e)}")
            return None

    def oper_score(self, openId: str, user_name: str, score: int, uface: str, oper: str) -> Optional[int]:
        """
        操作积分（主要接口）
        :param openId: 用户开放平台id
        :param user_name: 用户名称
        :param score: 积分数量
        :param uface: 用户头像
        :param oper: 操作类型
        :return: 操作后的积分，失败返回None
        """
        if not self._validate_operation_params(openId, user_name, score, oper):
            return None
            
        try:
            # 先刷新/创建用户信息
            user_info = self.scoreDB.user_refresh(openId, user_name, uface)
            if not user_info:
                self.log.error(f"用户信息刷新失败: {openId}")
                return None
            
            # 根据操作类型处理积分
            if oper == ScoreOperationType.recharge.value:
                return self.recharge_score(openId, score, oper)
            elif oper == ScoreOperationType.deduct.value:
                return self.recharge_score(openId, -score, oper)
            elif oper == ScoreOperationType.chat.value:
                # 聊天积分有每日上限控制
                return self._handle_chat_score(openId, user_name, score, oper)
            else:
                # 其他操作（唱歌、跳舞、绘画等）在后台线程处理以提高响应速度
                self._handle_async_score_operation(openId, user_name, score, oper)
                return user_info.get('score', 0)  # 返回当前积分
            
        except Exception as e:
            self.log.error(f"操作积分异常：{str(e)}")
            return None

    def _handle_chat_score(self, openId: str, user_name: str, score: int, oper: str) -> Optional[int]:
        """处理聊天积分（有每日上限限制）"""
        try:
            # 检查每日聊天积分上限
            today = time.strftime("%Y-%m-%d")
            daily_key = f"{openId}_{today}"
            
            daily_score = self._daily_score_cache.get(daily_key, 0)
            if daily_score >= ScoreSystemConfig.MAX_DAILY_CHAT_SCORE:
                self.log.info(f"用户{user_name}今日聊天积分已达上限")
                return None
            
            # 调整积分，确保不超过每日上限
            actual_score = min(score, ScoreSystemConfig.MAX_DAILY_CHAT_SCORE - daily_score)
            
            if actual_score > 0:
                success = self.scoreDB.oper_score(openId, user_name, actual_score, oper)
                if success:
                    self._daily_score_cache[daily_key] = daily_score + actual_score
                    
                    # 获取更新后的积分
                    user_info = self.scoreDB.get_score(openId)
                    new_score = user_info.get('score', 0) if user_info else 0
                    
                    # 广播积分变更
                    if user_info:
                        self._broadcast_score_change(openId, user_name, actual_score, oper, user_info.get('userface', ''))
                    
                    return new_score
            
            return None
            
        except Exception as e:
            self.log.error(f"处理聊天积分异常: {str(e)}")
            return None

    def _handle_async_score_operation(self, openId: str, user_name: str, score: int, oper: str):
        """异步处理积分操作"""
        def async_operation():
            try:
                # 根据操作类型计算实际积分变化
                actual_score = ScoreSystemConfig.get_operation_score(oper)
                if actual_score == 0:
                    actual_score = score  # 使用传入的积分值
                
                self.scoreDB.oper_score(openId, user_name, actual_score, oper)
                self.log.info(f"异步积分操作完成: {openId}, {oper}, {actual_score}")
            except Exception as e:
                self.log.error(f"异步积分操作失败: {str(e)}")
        
        score_thread = threading.Thread(target=async_operation, daemon=True)
        score_thread.start()

    def _broadcast_score_change(self, openId: str, user_name: str, score: int, oper: str, userface: str):
        """广播积分变更消息"""
        if not self.commonWebsocket:
            return  # WebSocket不可用时静默返回
            
        try:
            broadcast_data = {
                "type": "积分提示",
                "openId": openId,
                "username": user_name,
                "userface": userface,
                "score": score,
                "oper": oper,
                "timestamp": int(time.time())
            }
            
            # 异步广播，避免阻塞
            def broadcast():
                try:
                    self.commonWebsocket.broadcast(json.dumps(broadcast_data))
                except Exception as e:
                    self.log.error(f"广播消息失败: {e}")
            
            threading.Thread(target=broadcast, daemon=True).start()
            
        except Exception as e:
            self.log.error(f"准备广播消息失败: {str(e)}")

    def find_score_rank(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        积分排行榜查询
        :param limit: 返回数量，默认10
        :return: 排行榜列表
        """
        try:
            # 限制查询数量
            if limit < 1:
                limit = 10
            elif limit > ScoreSystemConfig.MAX_RANK_LIMIT:
                limit = ScoreSystemConfig.MAX_RANK_LIMIT
            
            # 从数据库获取排行榜数据
            rank_data = self.scoreDB.find_score_rank(limit)
            if not rank_data:
                return []
            
            # 处理排行榜数据
            rank_list = []
            for index, user in enumerate(rank_data, 1):
                rank_item = {
                    "rank": index,
                    "openId": user.get("openId"),
                    "userName": user.get("userName"),
                    "score": user.get("score", 0),
                    "userface": user.get("userface", ""),
                    "rank_title": ScoreRankConfig.get_rank_title(index)
                }
                rank_list.append(rank_item)
            
            return rank_list
            
        except Exception as e:
            self.log.error(f"查询积分排行榜失败：{str(e)}")
            return []

    def find_score_user(self, openId: str) -> Optional[Dict[str, Any]]:
        """
        查询用户积分信息
        :param openId: 用户开放平台id
        :return: 用户积分信息
        """
        if not openId:
            return None
            
        try:
            user_info = self.scoreDB.get_score(openId)
            if user_info:
                return {
                    "openId": openId,
                    "userName": user_info.get("userName", ""),
                    "score": user_info.get("score", 0),
                    "userface": user_info.get("userface", ""),
                    "updateTime": user_info.get("updateTime", ""),
                    "createTime": user_info.get("createTime", "")
                }
            return None
            
        except Exception as e:
            self.log.error(f"查询用户积分失败：{str(e)}")
            return None

    def user_refresh(self, openId: str, userName: str, face: str) -> bool:
        """
        用户信息刷新
        :param openId: 用户开放平台id
        :param userName: 用户名
        :param face: 用户头像
        :return: 是否成功
        """
        try:
            result = self.scoreDB.user_refresh(openId, userName, face)
            return result is not None
            
        except Exception as e:
            self.log.error(f"刷新用户信息失败：{str(e)}")
            return False

    def get_system_stats(self) -> Dict[str, Any]:
        """
        获取系统积分统计信息
        :return: 统计信息
        """
        try:
            user_count = self.scoreDB.get_user_count()
            total_score = self.scoreDB.get_total_score()
            
            # 计算附加统计信息
            stats = {
                "total_users": user_count,
                "total_score": total_score,
                "average_score": round(total_score / user_count, 2) if user_count > 0 else 0,
                "active_users_today": self._get_active_users_today(),
                "score_operations_today": self._get_score_operations_today(),
                "top_user": self._get_top_user()
            }
            
            return stats
            
        except Exception as e:
            self.log.error(f"获取系统统计失败：{str(e)}")
            return {
                "total_users": 0,
                "total_score": 0,
                "average_score": 0,
                "active_users_today": 0,
                "score_operations_today": 0,
                "top_user": None
            }

    def _get_active_users_today(self) -> int:
        """获取今日活跃用户数量"""
        try:
            today = time.strftime("%Y-%m-%d")
            return len([key for key in self._daily_score_cache.keys() if today in key])
        except:
            return 0

    def _get_score_operations_today(self) -> int:
        """获取今日积分操作次数"""
        try:
            today = time.strftime("%Y-%m-%d")
            return sum(self._daily_score_cache.get(key, 0) for key in self._daily_score_cache.keys() if today in key)
        except:
            return 0

    def _get_top_user(self) -> Optional[Dict[str, Any]]:
        """获取积分最高的用户"""
        try:
            rank_list = self.find_score_rank(1)
            return rank_list[0] if rank_list else None
        except:
            return None

    def batch_score_operation(self, operations: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        批量积分操作
        :param operations: 操作列表
        :return: 操作结果统计
        """
        try:
            success_count = 0
            failed_count = 0
            
            # 验证操作数量限制
            if len(operations) > ScoreSystemConfig.BATCH_OPERATION_SIZE:
                return {
                    "success": False,
                    "message": f"批量操作数量超过限制({ScoreSystemConfig.BATCH_OPERATION_SIZE})",
                    "processed": 0,
                    "success_count": 0,
                    "failed_count": 0
                }
            
            # 执行批量操作
            if self.scoreDB.batch_update_scores(operations):
                success_count = len(operations)
            else:
                failed_count = len(operations)
            
            return {
                "success": success_count > 0,
                "message": "批量操作完成",
                "processed": len(operations),
                "success_count": success_count,
                "failed_count": failed_count
            }
            
        except Exception as e:
            self.log.error(f"批量积分操作失败：{str(e)}")
            return {
                "success": False,
                "message": f"批量操作异常: {str(e)}",
                "processed": 0,
                "success_count": 0,
                "failed_count": len(operations) if operations else 0
            }

    def msg_deal_score_rank(self) -> str:
        """处理积分排行榜语音播报"""
        if not self.ttsCore:
            return "语音播报功能不可用"
            
        try:
            saystr = "大家好，现在公布直播间积分排行榜数据。"
            
            # 获取排行榜数据并处理
            data = self.find_score_rank(10)
            if data:
                for item in data:
                    rank_title = item.get('rank_title', f"第{item['rank']}名")
                    saystr += f"{rank_title}{item['userName']}的积分是{item['score']}分，"
            else:
                saystr += "暂时没有积分数据。"
            
            saystr += "以上是积分前10位同学。"
            
            # 异步播报，避免阻塞
            def tts_say():
                try:
                    self.ttsCore.assistant_tts_say(saystr)
                except Exception as e:
                    self.log.error(f"TTS播报失败: {e}")
            
            threading.Thread(target=tts_say, daemon=True).start()
            
            return saystr
            
        except Exception as e:
            self.log.error(f"处理积分排行榜播报失败：{str(e)}")
            return "积分排行榜暂时无法获取"

    def check_user_score_sufficient(self, openId: str, required_score: int) -> bool:
        """
        检查用户积分是否足够
        :param openId: 用户ID
        :param required_score: 需要的积分
        :return: 是否足够
        """
        try:
            user_info = self.scoreDB.get_score(openId)
            if not user_info:
                return False
            
            current_score = user_info.get('score', 0)
            return current_score >= required_score
            
        except Exception as e:
            self.log.error(f"检查用户积分失败：{str(e)}")
            return False

    def get_daily_chat_remaining(self, openId: str) -> int:
        """
        获取用户今日剩余聊天积分
        :param openId: 用户ID
        :return: 剩余聊天积分
        """
        try:
            today = time.strftime("%Y-%m-%d")
            daily_key = f"{openId}_{today}"
            
            daily_score = self._daily_score_cache.get(daily_key, 0)
            remaining = ScoreSystemConfig.MAX_DAILY_CHAT_SCORE - daily_score
            
            return max(0, remaining)
            
        except Exception as e:
            self.log.error(f"获取剩余聊天积分失败：{str(e)}")
            return 0

    def reset_daily_cache(self):
        """重置每日缓存（可由定时任务调用）"""
        try:
            today = time.strftime("%Y-%m-%d")
            # 清理过期的缓存数据
            keys_to_remove = [key for key in self._daily_score_cache.keys() if today not in key]
            for key in keys_to_remove:
                del self._daily_score_cache[key]
            
            self.log.info(f"每日缓存重置完成，清理了{len(keys_to_remove)}个过期缓存")
            
        except Exception as e:
            self.log.error(f"重置每日缓存失败：{str(e)}")

    def _validate_score_operation(self, openId: str, score: int) -> bool:
        """验证积分操作参数"""
        if not openId or not isinstance(openId, str):
            self.log.error(f"无效的openId: {openId}")
            return False
        if not isinstance(score, int):
            self.log.error(f"无效的score类型: {type(score)}")
            return False
        if not ScoreSystemConfig.validate_score_amount(score):
            self.log.error(f"积分数量超出允许范围: {score}")
            return False
        return True

    def _validate_operation_params(self, openId: str, user_name: str, score: int, oper: str) -> bool:
        """验证操作参数"""
        if not openId or not isinstance(openId, str):
            self.log.error(f"无效的openId: {openId}")
            return False
        if not user_name or not isinstance(user_name, str):
            self.log.error(f"无效的user_name: {user_name}")
            return False
        if not isinstance(score, int):
            self.log.error(f"无效的score类型: {type(score)}")
            return False
        if not oper or not isinstance(oper, str):
            self.log.error(f"无效的oper: {oper}")
            return False
        if not ScoreSystemConfig.validate_operation_type(oper):
            self.log.error(f"无效的操作类型: {oper}")
            return False
        return True

    def get_score_config(self) -> Dict[str, Any]:
        """获取积分配置"""
        return ScoreSystemConfig.get_config_dict()

    def get_component_status(self) -> Dict[str, bool]:
        """获取组件状态"""
        return {
            "websocket": self.commonWebsocket is not None,
            "tts": self.ttsCore is not None,
            "common_data": self.commonData is not None,
            "score_db": self.scoreDB is not None
        }