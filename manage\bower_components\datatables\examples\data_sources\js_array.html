<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>
	<head>
		<meta http-equiv="content-type" content="text/html; charset=utf-8" />
		<link rel="shortcut icon" type="image/ico" href="http://www.datatables.net/media/images/favicon.ico" />
		
		<title>DataTables example</title>
		<style type="text/css" title="currentStyle">
			@import "../../media/css/demo_page.css";
			@import "../../media/css/demo_table.css";
		</style>
		<script type="text/javascript" language="javascript" src="../../media/js/jquery.js"></script>
		<script type="text/javascript" language="javascript" src="../../media/js/jquery.dataTables.js"></script>
		<script type="text/javascript" charset="utf-8">
			/* Data set - can contain whatever information you want */
			var aDataSet = [
				['Trident','Internet Explorer 4.0','Win 95+','4','X'],
				['Trident','Internet Explorer 5.0','Win 95+','5','C'],
				['Trident','Internet Explorer 5.5','Win 95+','5.5','A'],
				['Trident','Internet Explorer 6','Win 98+','6','A'],
				['Trident','Internet Explorer 7','Win XP SP2+','7','A'],
				['Trident','AOL browser (AOL desktop)','Win XP','6','A'],
				['Gecko','Firefox 1.0','Win 98+ / OSX.2+','1.7','A'],
				['Gecko','Firefox 1.5','Win 98+ / OSX.2+','1.8','A'],
				['Gecko','Firefox 2.0','Win 98+ / OSX.2+','1.8','A'],
				['Gecko','Firefox 3.0','Win 2k+ / OSX.3+','1.9','A'],
				['Gecko','Camino 1.0','OSX.2+','1.8','A'],
				['Gecko','Camino 1.5','OSX.3+','1.8','A'],
				['Gecko','Netscape 7.2','Win 95+ / Mac OS 8.6-9.2','1.7','A'],
				['Gecko','Netscape Browser 8','Win 98SE+','1.7','A'],
				['Gecko','Netscape Navigator 9','Win 98+ / OSX.2+','1.8','A'],
				['Gecko','Mozilla 1.0','Win 95+ / OSX.1+',1,'A'],
				['Gecko','Mozilla 1.1','Win 95+ / OSX.1+',1.1,'A'],
				['Gecko','Mozilla 1.2','Win 95+ / OSX.1+',1.2,'A'],
				['Gecko','Mozilla 1.3','Win 95+ / OSX.1+',1.3,'A'],
				['Gecko','Mozilla 1.4','Win 95+ / OSX.1+',1.4,'A'],
				['Gecko','Mozilla 1.5','Win 95+ / OSX.1+',1.5,'A'],
				['Gecko','Mozilla 1.6','Win 95+ / OSX.1+',1.6,'A'],
				['Gecko','Mozilla 1.7','Win 98+ / OSX.1+',1.7,'A'],
				['Gecko','Mozilla 1.8','Win 98+ / OSX.1+',1.8,'A'],
				['Gecko','Seamonkey 1.1','Win 98+ / OSX.2+','1.8','A'],
				['Gecko','Epiphany 2.20','Gnome','1.8','A'],
				['Webkit','Safari 1.2','OSX.3','125.5','A'],
				['Webkit','Safari 1.3','OSX.3','312.8','A'],
				['Webkit','Safari 2.0','OSX.4+','419.3','A'],
				['Webkit','Safari 3.0','OSX.4+','522.1','A'],
				['Webkit','OmniWeb 5.5','OSX.4+','420','A'],
				['Webkit','iPod Touch / iPhone','iPod','420.1','A'],
				['Webkit','S60','S60','413','A'],
				['Presto','Opera 7.0','Win 95+ / OSX.1+','-','A'],
				['Presto','Opera 7.5','Win 95+ / OSX.2+','-','A'],
				['Presto','Opera 8.0','Win 95+ / OSX.2+','-','A'],
				['Presto','Opera 8.5','Win 95+ / OSX.2+','-','A'],
				['Presto','Opera 9.0','Win 95+ / OSX.3+','-','A'],
				['Presto','Opera 9.2','Win 88+ / OSX.3+','-','A'],
				['Presto','Opera 9.5','Win 88+ / OSX.3+','-','A'],
				['Presto','Opera for Wii','Wii','-','A'],
				['Presto','Nokia N800','N800','-','A'],
				['Presto','Nintendo DS browser','Nintendo DS','8.5','C/A<sup>1</sup>'],
				['KHTML','Konqureror 3.1','KDE 3.1','3.1','C'],
				['KHTML','Konqureror 3.3','KDE 3.3','3.3','A'],
				['KHTML','Konqureror 3.5','KDE 3.5','3.5','A'],
				['Tasman','Internet Explorer 4.5','Mac OS 8-9','-','X'],
				['Tasman','Internet Explorer 5.1','Mac OS 7.6-9','1','C'],
				['Tasman','Internet Explorer 5.2','Mac OS 8-X','1','C'],
				['Misc','NetFront 3.1','Embedded devices','-','C'],
				['Misc','NetFront 3.4','Embedded devices','-','A'],
				['Misc','Dillo 0.8','Embedded devices','-','X'],
				['Misc','Links','Text only','-','X'],
				['Misc','Lynx','Text only','-','X'],
				['Misc','IE Mobile','Windows Mobile 6','-','C'],
				['Misc','PSP browser','PSP','-','C'],
				['Other browsers','All others','-','-','U']
			];
			
			$(document).ready(function() {
				$('#dynamic').html( '<table cellpadding="0" cellspacing="0" border="0" class="display" id="example"></table>' );
				$('#example').dataTable( {
					"aaData": aDataSet,
					"aoColumns": [
						{ "sTitle": "Engine" },
						{ "sTitle": "Browser" },
						{ "sTitle": "Platform" },
						{ "sTitle": "Version", "sClass": "center" },
						{ "sTitle": "Grade", "sClass": "center" }
					]
				} );	
			} );
		</script>
	</head>
	<body id="dt_example">
		<div id="container">
			<div class="full_width big">
				DataTables dynamic creation example
			</div>
			
			<h1>Preamble</h1>
			<p>At times you will wish to be able to create a table from dynamic information passed directly to DataTables, rather than having it read from the document. This is achieved using the "aaData" array in the initialisation object. A table node must first be created before the initialiser is called (as shown in the code below). This is also useful for optimisation - if you are able to format the data as required, this method can save a lot of DOM parsing to create a table.</p>
			
			<h1>Live example</h1>
			<div id="dynamic"></div>
			<div class="spacer"></div>
			
			
			<h1>Initialisation code</h1>
			<pre class="brush: js;">$(document).ready(function() {
	$('#demo').html( '&lt;table cellpadding="0" cellspacing="0" border="0" class="display" id="example"&gt;&lt;/table&gt;' );
	$('#example').dataTable( {
		"aaData": [
			/* Reduced data set */
			[ "Trident", "Internet Explorer 4.0", "Win 95+", 4, "X" ],
			[ "Trident", "Internet Explorer 5.0", "Win 95+", 5, "C" ],
			[ "Trident", "Internet Explorer 5.5", "Win 95+", 5.5, "A" ],
			[ "Trident", "Internet Explorer 6.0", "Win 98+", 6, "A" ],
			[ "Trident", "Internet Explorer 7.0", "Win XP SP2+", 7, "A" ],
			[ "Gecko", "Firefox 1.5", "Win 98+ / OSX.2+", 1.8, "A" ],
			[ "Gecko", "Firefox 2", "Win 98+ / OSX.2+", 1.8, "A" ],
			[ "Gecko", "Firefox 3", "Win 2k+ / OSX.3+", 1.9, "A" ],
			[ "Webkit", "Safari 1.2", "OSX.3", 125.5, "A" ],
			[ "Webkit", "Safari 1.3", "OSX.3", 312.8, "A" ],
			[ "Webkit", "Safari 2.0", "OSX.4+", 419.3, "A" ],
			[ "Webkit", "Safari 3.0", "OSX.4+", 522.1, "A" ]
		],
		"aoColumns": [
			{ "sTitle": "Engine" },
			{ "sTitle": "Browser" },
			{ "sTitle": "Platform" },
			{ "sTitle": "Version", "sClass": "center" },
			{ "sTitle": "Grade", "sClass": "center" }
		]
	} );	
} );</pre>
			<style type="text/css">
				@import "../examples_support/syntax/css/shCore.css";
			</style>
			<script type="text/javascript" language="javascript" src="../examples_support/syntax/js/shCore.js"></script>
			
			
			<h1>Other examples</h1>
			<div class="demo_links">
				<h2>Basic initialisation</h2>
				<ul>
					<li><a href="../basic_init/zero_config.html">Zero configuration</a></li>
					<li><a href="../basic_init/filter_only.html">Feature enablement</a></li>
					<li><a href="../basic_init/table_sorting.html">Sorting data</a></li>
					<li><a href="../basic_init/multi_col_sort.html">Multi-column sorting</a></li>
					<li><a href="../basic_init/multiple_tables.html">Multiple tables</a></li>
					<li><a href="../basic_init/hidden_columns.html">Hidden columns</a></li>
					<li><a href="../basic_init/complex_header.html">Complex headers - grouping with colspan</a></li>
					<li><a href="../basic_init/dom.html">DOM positioning</a></li>
					<li><a href="../basic_init/flexible_width.html">Flexible table width</a></li>
					<li><a href="../basic_init/state_save.html">State saving</a></li>
					<li><a href="../basic_init/alt_pagination.html">Alternative pagination styles</a></li>
					<li>Scrolling: <br>
						<a href="../basic_init/scroll_x.html">Horizontal</a> / 
						<a href="../basic_init/scroll_y.html">Vertical</a> / 
						<a href="../basic_init/scroll_xy.html">Both</a> / 
						<a href="../basic_init/scroll_y_theme.html">Themed</a> / 
						<a href="../basic_init/scroll_y_infinite.html">Infinite</a>
					</li>
					<li><a href="../basic_init/language.html">Change language information (internationalisation)</a></li>
					<li><a href="../basic_init/themes.html">ThemeRoller themes (Smoothness)</a></li>
				</ul>
				
				<h2>Advanced initialisation</h2>
				<ul>
					<li>Events: <br>
						<a href="../advanced_init/events_live.html">Live events</a> / 
						<a href="../advanced_init/events_pre_init.html">Pre-init</a> / 
						<a href="../advanced_init/events_post_init.html">Post-init</a>
					</li>
					<li><a href="../advanced_init/column_render.html">Column rendering</a></li>
					<li><a href="../advanced_init/html_sort.html">Sorting without HTML tags</a></li>
					<li><a href="../advanced_init/dom_multiple_elements.html">Multiple table controls (sDom)</a></li>
					<li><a href="../advanced_init/length_menu.html">Defining length menu options</a></li>
					<li><a href="../advanced_init/complex_header.html">Complex headers and hidden columns</a></li>
					<li><a href="../advanced_init/dom_toolbar.html">Custom toolbar (element) around table</a></li>
					<li><a href="../advanced_init/highlight.html">Row highlighting with CSS</a></li>
					<li><a href="../advanced_init/row_grouping.html">Row grouping</a></li>
					<li><a href="../advanced_init/row_callback.html">Row callback</a></li>
					<li><a href="../advanced_init/footer_callback.html">Footer callback</a></li>
					<li><a href="../advanced_init/sorting_control.html">Control sorting direction of columns</a></li>
					<li><a href="../advanced_init/language_file.html">Change language information from a file (internationalisation)</a></li>
					<li><a href="../advanced_init/defaults.html">Setting defaults</a></li>
					<li><a href="../advanced_init/localstorage.html">State saving with localStorage</a></li>
					<li><a href="../advanced_init/dt_events.html">Custom events</a></li>
				</ul>
				
				<h2>API</h2>
				<ul>
					<li><a href="../api/add_row.html">Dynamically add a new row</a></li>
					<li><a href="../api/multi_filter.html">Individual column filtering (using "input" elements)</a></li>
					<li><a href="../api/multi_filter_select.html">Individual column filtering (using "select" elements)</a></li>
					<li><a href="../api/highlight.html">Highlight rows and columns</a></li>
					<li><a href="../api/row_details.html">Show and hide details about a particular record</a></li>
					<li><a href="../api/select_row.html">User selectable rows (multiple rows)</a></li>
					<li><a href="../api/select_single_row.html">User selectable rows (single row) and delete rows</a></li>
					<li><a href="../api/editable.html">Editable rows (with jEditable)</a></li>
					<li><a href="../api/form.html">Submit form with elements in table</a></li>
					<li><a href="../api/counter_column.html">Index column (static number column)</a></li>
					<li><a href="../api/show_hide.html">Show and hide columns dynamically</a></li>
					<li><a href="../api/api_in_init.html">API function use in initialisation object (callback)</a></li>
					<li><a href="../api/tabs_and_scrolling.html">DataTables scrolling and tabs</a></li>
					<li><a href="../api/regex.html">Regular expression filtering</a></li>
				</ul>
			</div>
			
			<div class="demo_links">
				<h2>Data sources</h2>
				<ul>
					<li><a href="../data_sources/dom.html">DOM</a></li>
					<li><a href="../data_sources/js_array.html">Javascript array</a></li>
					<li><a href="../data_sources/ajax.html">Ajax source</a></li>
					<li><a href="../data_sources/server_side.html">Server side processing</a></li>
				</ul>
				
				<h2>Server-side processing</h2>
				<ul>
					<li><a href="../server_side/server_side.html">Obtain server-side data</a></li>
					<li><a href="../server_side/custom_vars.html">Add extra HTTP variables</a></li>
					<li><a href="../server_side/post.html">Use HTTP POST</a></li>
					<li><a href="../server_side/ids.html">Automatic addition of IDs and classes to rows</a></li>
					<li><a href="../server_side/object_data.html">Reading table data from objects</a></li>
					<li><a href="../server_side/row_details.html">Show and hide details about a particular record</a></li>
					<li><a href="../server_side/select_rows.html">User selectable rows (multiple rows)</a></li>
					<li><a href="../server_side/jsonp.html">JSONP for a cross domain data source</a></li>
					<li><a href="../server_side/editable.html">jEditable integration with DataTables</a></li>
					<li><a href="../server_side/defer_loading.html">Deferred loading of Ajax data</a></li>
					<li><a href="../server_side/pipeline.html">Pipelining data (reduce Ajax calls for paging)</a></li>
				</ul>
				
				<h2>Ajax data source</h2>
				<ul>
					<li><a href="../ajax/ajax.html">Ajax sourced data (array of arrays)</a></li>
					<li><a href="../ajax/objects.html">Ajax sourced data (array of objects)</a></li>
					<li><a href="../ajax/defer_render.html">Deferred DOM creation for extra speed</a></li>
					<li><a href="../ajax/null_data_source.html">Empty data source columns</a></li>
					<li><a href="../ajax/custom_data_property.html">Use a data source other than aaData (the default)</a></li>
					<li><a href="../ajax/objects_subarrays.html">Read column data from sub-arrays</a></li>
					<li><a href="../ajax/deep.html">Read column data from deeply nested properties</a></li>
				</ul>
				
				<h2>Plug-ins</h2>
				<ul>
					<li><a href="../plug-ins/plugin_api.html">Add custom API functions</a></li>
					<li><a href="../plug-ins/sorting_plugin.html">Sorting and automatic type detection</a></li>
					<li><a href="../plug-ins/sorting_sType.html">Sorting without automatic type detection</a></li>
					<li><a href="../plug-ins/paging_plugin.html">Custom pagination controls</a></li>
					<li><a href="../plug-ins/range_filtering.html">Range filtering / custom filtering</a></li>
					<li><a href="../plug-ins/dom_sort.html">Live DOM sorting</a></li>
					<li><a href="../plug-ins/html_sort.html">Automatic HTML type detection</a></li>
				</ul>
			</div>
			
			
			<div id="footer" class="clear" style="text-align:center;">
				<p>
					Please refer to the <a href="http://www.datatables.net/usage">DataTables documentation</a> for full information about its API properties and methods.<br>
					Additionally, there are a wide range of <a href="http://www.datatables.net/extras">extras</a> and <a href="http://www.datatables.net/plug-ins">plug-ins</a> which extend the capabilities of DataTables.
				</p>
				
				<span style="font-size:10px;">
					DataTables designed and created by <a href="http://www.sprymedia.co.uk">Allan Jardine</a> &copy; 2007-2011<br>
					DataTables is dual licensed under the <a href="http://www.datatables.net/license_gpl2">GPL v2 license</a> or a <a href="http://www.datatables.net/license_bsd">BSD (3-point) license</a>.
				</span>
			</div>
		</div>
	</body>
</html>