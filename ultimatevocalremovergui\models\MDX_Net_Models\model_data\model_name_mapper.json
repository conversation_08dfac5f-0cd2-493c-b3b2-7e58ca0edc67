{"UVR_MDXNET_1_9703": "UVR-MDX-NET 1", "UVR_MDXNET_2_9682": "UVR-MDX-NET 2", "UVR_MDXNET_3_9662": "UVR-MDX-NET 3", "UVR_MDXNET_KARA": "UVR-MDX-NET Karaoke", "UVR_MDXNET_Main": "UVR-MDX-NET Main", "UVR-MDX-NET-Inst_1": "UVR-MDX-NET Inst 1", "UVR-MDX-NET-Inst_2": "UVR-MDX-NET Inst 2", "UVR-MDX-NET-Inst_3": "UVR-MDX-NET Inst 3", "UVR-MDX-NET-Inst_4": "UVR-MDX-NET Inst 4", "UVR-MDX-NET-Inst_Main": "UVR-MDX-NET Inst Main", "UVR-MDX-NET-Inst_Main_2": "UVR-MDX-NET Inst Main 2", "UVR-MDX-NET-Inst_HQ_1": "UVR-MDX-NET Inst HQ 1", "UVR-MDX-NET-Inst_HQ_2": "UVR-MDX-NET Inst HQ 2", "UVR-MDX-NET-Inst_HQ_3": "UVR-MDX-NET Inst HQ 3", "UVR-MDX-NET-Inst_HQ_4": "UVR-MDX-NET Inst HQ 4", "UVR-MDX-NET-Inst_HQ_5": "UVR-MDX-NET Inst HQ 5", "UVR-MDX-NET_Crowd_HQ_1": "UVR-MDX-NET Crowd HQ 1", "UVR_MDXNET_KARA_2": "UVR-MDX-NET Karaoke 2", "Kim_Vocal_1": "Kim <PERSON> 1", "Kim_Vocal_2": "<PERSON> 2", "Kim_Inst": "<PERSON>", "MDX23C-8KFFT-InstVoc_HQ.ckpt": "MDX23C-InstVoc HQ", "MDX23C-8KFFT-InstVoc_HQ_2.ckpt": "MDX23C-InstVoc HQ 2", "MDX23C_D1581.ckpt": "MDX23C-InstVoc D1581", "Reverb_HQ_By_FoxJoy": "Reverb HQ", "model_bs_roformer_ep_317_sdr_12.9755.ckpt": "BS-Roformer-Viperx-1297", "model_bs_roformer_ep_368_sdr_12.9628.ckpt": "BS-Roformer-Viperx-1296", "model_bs_roformer_ep_937_sdr_10.5309.ckpt": "BS-Roformer-Viperx-1053", "model_mel_band_roformer_ep_3005_sdr_11.4360.ckpt": "Mel-Roformer-Viperx-1143", "melband_roformer_inst_v1.ckpt": "MB-Roformer-Inst-v1", "melband_roformer_inst_v2.ckpt": "MB-Roformer-Inst-v2", "inst_v1e.ckpt": "MB-Roformer-Inst-v1-E", "melband_roformer_instvoc_duality_v1.ckpt": "MB-Roformer-InstVoc-Duality-v1", "melband_roformer_instvox_duality_v2.ckpt": "MB-Roformer-InstVoc-Duality-v2", "MelBandRoformer.ckpt": "MB-R<PERSON><PERSON><PERSON><PERSON><PERSON>", "BS_Inst_EXP_VRL.ckpt": "BS-Ro-Inst-EXP-Unwa", "mel_band_roformer_karaoke_aufr33_viperx_sdr_10.1956.ckpt": "MB-Ro-Kara-Aufr33-Viperx", "deverb_bs_roformer_8_256dim_8depth.ckpt": "BS-Ro-Dereverb-Anvuew", "model_bandit_plus_dnr_sdr_11.47.ckpt": "Cinematic-Bandit-Plus", "checkpoint-multi_fixed.ckpt": "Cinematic-Bandit-Multi", "scnet_checkpoint_musdb18.ckpt": "4S-SCNet-Starrytong", "SCNet-large_starrytong_fixed.ckpt": "4S-SCNet-Large-Starrytong", "model_scnet_sdr_9.3244.ckpt": "4S-SCNet-Large", "aufr33-jarredou_DrumSep_model_mdx23c_ep_141_sdr_10.8059.ckpt": "DrumSep-Aufr33-<PERSON><PERSON><PERSON><PERSON>", "model_mdx23c_ep_271_l1_freq_72.2383.ckpt": "Phantom-Mid-Wesleyr36", "model_scnet_ep_54_sdr_9.8051.ckpt": "4S-SCNet-XL-ZFTurbo"}