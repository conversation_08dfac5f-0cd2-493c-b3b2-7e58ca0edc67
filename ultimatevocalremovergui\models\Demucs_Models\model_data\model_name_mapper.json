{"tasnet.th": "v1 | Tasnet", "tasnet_extra.th": "v1 | Tasnet_extra", "demucs.th": "v1 | <PERSON><PERSON><PERSON>", "demucs_extra.th": "v1 | Demucs_extra", "light.th": "v1 | Light", "light_extra.th": "v1 | Light_extra", "tasnet.th.gz": "v1 | Tasnet.gz", "tasnet_extra.th.gz": "v1 | Tasnet_extra.gz", "demucs.th.gz": "v1 | Demucs_extra.gz", "light.th.gz": "v1 | Light.gz", "light_extra.th.gz": "v1 | Light_extra.gz", "tasnet-beb46fac.th": "v2 | Tasnet", "tasnet_extra-df3777b2.th": "v2 | Tasnet_extra", "demucs48_hq-28a1282c.th": "v2 | Demucs48_hq", "demucs-e07c671f.th": "v2 | <PERSON><PERSON><PERSON>", "demucs_extra-3646af93.th": "v2 | Demucs_extra", "demucs_unittest-09ebc15f.th": "v2 | Demucs_unittest", "mdx.yaml": "v3 | mdx", "mdx_extra.yaml": "v3 | mdx_extra", "mdx_extra_q.yaml": "v3 | mdx_extra_q", "mdx_q.yaml": "v3 | mdx_q", "repro_mdx_a.yaml": "v3 | repro_mdx_a", "repro_mdx_a_hybrid_only.yaml": "v3 | repro_mdx_a_hybrid", "repro_mdx_a_time_only.yaml": "v3 | repro_mdx_a_time", "UVR_Demucs_Model_1.yaml": "v3 | UVR_Model_1", "UVR_Demucs_Model_2.yaml": "v3 | UVR_Model_2", "UVR_Demucs_Model_Bag.yaml": "v3 | UVR_Model_Bag", "hdemucs_mmi.yaml": "v4 | hdemucs_mmi", "htdemucs.yaml": "v4 | htdemucs", "htdemucs_ft.yaml": "v4 | htdemucs_ft", "htdemucs_6s.yaml": "v4 | htdemucs_6s", "UVR_Demucs_Model_ht.yaml": "v4 | UVR_Model_ht"}