audio:
  chunk_size: 485100 # 44100 * 11
  num_channels: 2
  sample_rate: 44100
  min_mean_abs: 0.000

model:
  sources: ['drums', 'bass', 'other', 'vocals']
  audio_channels: 2
  dims: [4, 32, 64, 128]
  nfft: 4096
  hop_size: 1024
  win_size: 4096
  normalized: True
  band_SR: [0.175, 0.392, 0.433]
  band_stride: [1, 4, 16]
  band_kernel: [3, 4, 16]
  conv_depths: [3, 2, 1]
  compress: 4
  conv_kernel: 3
  num_dplayer: 6
  expand: 1
  tran_rotary_embedding_dim: 64
  tran_depth: 1
  tran_heads: 8
  tran_dim_head: 64
  tran_attn_dropout: 0.0
  tran_ff_dropout: 0.0
  tran_flash_attn: False

training:
  batch_size: 5
  gradient_accumulation_steps: 1
  grad_clip: 0
  instruments: ['drums', 'bass', 'other', 'vocals']
  patience: 2
  reduce_factor: 0.95
  target_instrument: null
  num_epochs: 1000
  num_steps: 1000
  q: 0.95
  coarse_loss_clip: true
  ema_momentum: 0.999
  optimizer: adam
  lr: 5.0e-05
  # optimizer: prodigy
  # lr: 1.0
  normalize: false # perform normalization on input of model (use the same for inference!)
  other_fix: false # it's needed for checking on multisong dataset if other is actually instrumental
  use_amp: true # enable or disable usage of mixed precision (float16) - usually it must be true

augmentations:
  enable: true # enable or disable all augmentations (to fast disable if needed)
  loudness: true # randomly change loudness of each stem on the range (loudness_min; loudness_max)
  loudness_min: 0.5
  loudness_max: 1.5
  mixup: true # mix several stems of same type with some probability (only works for dataset types: 1, 2, 3)
  mixup_probs: !!python/tuple # 2 additional stems of the same type (1st with prob 0.2, 2nd with prob 0.02)
    - 0.2
    - 0.02
    - 0.002
  mixup_loudness_min: 0.5
  mixup_loudness_max: 1.5

  # apply mp3 compression to mixture only (emulate downloading mp3 from internet)
  mp3_compression_on_mixture: 0.01
  mp3_compression_on_mixture_bitrate_min: 32
  mp3_compression_on_mixture_bitrate_max: 320
  mp3_compression_on_mixture_backend: "lameenc"

  all:
    channel_shuffle: 0.5 # Set 0 or lower to disable
    random_inverse: 0.01 # inverse track (better lower probability)
    random_polarity: 0.5 # polarity change (multiply waveform to -1)

  vocals:
      pitch_shift: 0.1
      pitch_shift_min_semitones: -5
      pitch_shift_max_semitones: 5
      seven_band_parametric_eq: 0.1
      seven_band_parametric_eq_min_gain_db: -9
      seven_band_parametric_eq_max_gain_db: 9
      tanh_distortion: 0.1
      tanh_distortion_min: 0.1
      tanh_distortion_max: 0.7
      time_stretch: 0.01
      time_stretch_min_rate: 0.8
      time_stretch_max_rate: 1.25
  bass:
    pitch_shift: 0.01
    pitch_shift_min_semitones: -2
    pitch_shift_max_semitones: 2
    seven_band_parametric_eq: 0.01
    seven_band_parametric_eq_min_gain_db: -3
    seven_band_parametric_eq_max_gain_db: 6
    tanh_distortion: 0.01
    tanh_distortion_min: 0.1
    tanh_distortion_max: 0.5
    time_stretch: 0.1
    time_stretch_min_rate: 0.9
    time_stretch_max_rate: 1.1
  drums:
    pitch_shift: 0.1
    pitch_shift_min_semitones: -5
    pitch_shift_max_semitones: 5
    seven_band_parametric_eq: 0.1
    seven_band_parametric_eq_min_gain_db: -9
    seven_band_parametric_eq_max_gain_db: 9
    tanh_distortion: 0.1
    tanh_distortion_min: 0.1
    tanh_distortion_max: 0.6
    time_stretch: 0.01
    time_stretch_min_rate: 0.8
    time_stretch_max_rate: 1.25
  other:
    pitch_shift: 0.1
    pitch_shift_min_semitones: -4
    pitch_shift_max_semitones: 4
    gaussian_noise: 0.1
    gaussian_noise_min_amplitude: 0.001
    gaussian_noise_max_amplitude: 0.015
    time_stretch: 0.01
    time_stretch_min_rate: 0.8
    time_stretch_max_rate: 1.25

inference:
  batch_size: 2
  dim_t: 256
  num_overlap: 2
  normalize: false

loss_multistft:
  fft_sizes:
  - 1024
  - 2048
  - 4096
  hop_sizes:
  - 147
  - 256
  - 512
  win_lengths:
  - 1024
  - 2048
  - 4096
  window: "hann_window"
  scale: "mel"
  n_bins: 128
  sample_rate: 44100
  perceptual_weighting: true
  w_sc: 1.0
  w_log_mag: 1.0
  w_lin_mag: 0.0
  w_phs: 0.0
  mag_distance: "L1"