{"name": "bootstrap", "version": "3.2.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "bootstrap", "version": "3.2.0", "license": "MIT", "dependencies": {"bootstrap": "file:"}, "devDependencies": {"btoa": "~1.1.2", "glob": "~4.0.2", "grunt": "~0.4.5", "grunt-autoprefixer": "~0.7.6", "grunt-banner": "~0.2.3", "grunt-contrib-clean": "~0.5.0", "grunt-contrib-concat": "~0.4.0", "grunt-contrib-connect": "~0.8.0", "grunt-contrib-copy": "~0.5.0", "grunt-contrib-csslint": "~0.2.0", "grunt-contrib-cssmin": "~0.10.0", "grunt-contrib-jade": "~0.12.0", "grunt-contrib-jshint": "~0.10.0", "grunt-contrib-less": "~0.11.3", "grunt-contrib-qunit": "~0.5.1", "grunt-contrib-uglify": "~0.5.0", "grunt-contrib-watch": "~0.6.1", "grunt-csscomb": "~2.0.1", "grunt-exec": "~0.4.5", "grunt-html-validation": "~0.1.18", "grunt-jekyll": "~0.4.2", "grunt-jscs-checker": "~0.6.0", "grunt-saucelabs": "~8.1.0", "grunt-sed": "~0.1.1", "load-grunt-tasks": "~0.6.0", "markdown": "~0.5.0", "npm-shrinkwrap": "~3.1.6", "time-grunt": "~0.3.2"}, "engines": {"node": "~0.10.1"}}, "node_modules/abbrev": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/abbrev/-/abbrev-1.1.1.tgz", "integrity": "sha512-nne9/IiQ/hzIhY6pdDnbBtz7DjPTKrY00P/zvPSm5pOFkl6xuGrGnXn/VtTNNfNtAfZ9/1RtehkszU9qcTii0Q==", "dev": true}, "node_modules/accepts": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/accepts/-/accepts-1.0.3.tgz", "integrity": "sha512-cZqKqO3VXtuIZ5vQLVc8M6JDFVTZoVwZrlmTCA1nH9EoN5v6ZWWStKvd1A5RWpduRVXD55px3t75TvS7JdLfHA==", "dev": true, "dependencies": {"mime": "~1.2.11", "negotiator": "0.4.6"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/agent-base": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/agent-base/-/agent-base-1.0.2.tgz", "integrity": "sha512-IrdRInle5l28T2DjBsOojXniN91mXYkt9piDyPbPEoA/X+f7kjd0qiIb18vZThIZCJdLk2Zq/ukXxZp8NkcFsw==", "dev": true}, "node_modules/align-text": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/align-text/-/align-text-0.1.4.tgz", "integrity": "sha512-GrTZLRpmp6wIC2ztrWW9MjjTgSKccffgFagbNDOX95/dcjEcYZibYTeaOntySQLcdw1ztBoFkviiUvTMbb9MYg==", "dev": true, "dependencies": {"kind-of": "^3.0.2", "longest": "^1.0.1", "repeat-string": "^1.5.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/amdefine": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/amdefine/-/amdefine-1.0.1.tgz", "integrity": "sha512-S2Hw0TtNkMJhIabBwIojKL9YHO5T0n5eNqWJ7Lrlel/zDbftQpxpapi8tZs3X1HWa+u+QeydGmzzNU0m09+Rcg==", "dev": true, "engines": {"node": ">=0.4.2"}}, "node_modules/ansi-regex": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-0.2.1.tgz", "integrity": "sha512-sGwIGMjhYdW26/IhwK2gkWWI8DRCVO6uj3hYgHT+zD+QL1pa37tM3ujhyfcJIYSbsxp7Gxhy7zrRW/1AHm4BmA==", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/ansi-styles": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-1.0.0.tgz", "integrity": "sha512-3iF4FIKdxaVYT3JqQuY3Wat/T2t7TRbbQ94Fu50ZUCbLy4TFbTzr90NOHQodQkNqmeEGCw8WbeP78WNi6SKYUA==", "dev": true, "engines": {"node": ">=0.8.0"}}, "node_modules/ansicolors": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/ansicolors/-/ansicolors-0.2.1.tgz", "integrity": "sha512-tOIuy1/SK/dr94ZA0ckDohKXNeBNqZ4us6PjMVLs5h1w2GBB6uPtOknp2+VF4F/zcy9LI70W+Z+pE2Soajky1w==", "dev": true}, "node_modules/argparse": {"version": "0.1.16", "resolved": "https://registry.npmjs.org/argparse/-/argparse-0.1.16.tgz", "integrity": "sha512-LjmC2dNpdn2L4UzyoaIr11ELYoLn37ZFy9zObrQFHsSuOepeUEMKnM8w5KL4Tnrp2gy88rRuQt6Ky8Bjml+Baw==", "dev": true, "dependencies": {"underscore": "~1.7.0", "underscore.string": "~2.4.0"}}, "node_modules/argparse/node_modules/underscore.string": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/underscore.string/-/underscore.string-2.4.0.tgz", "integrity": "sha512-yxkabuCaIBnzfIvX3kBxQqCs0ar/bfJwDnFEHJUm/ZrRVhT3IItdRF5cZjARLzEnyQYtIUhsZ2LG2j3HidFOFQ==", "dev": true, "engines": {"node": "*"}}, "node_modules/array-differ": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/array-differ/-/array-differ-0.1.0.tgz", "integrity": "sha512-RGmBEbSH4jw1OLmdJtZEL5P6AvgKR9SZGF4KQoWnCmd+QVajPUNEIMSuS1q0ZEdsMUujKGICDsgg4znkzvKi3Q==", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/array-find": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/array-find/-/array-find-0.1.1.tgz", "integrity": "sha512-r40SlXgo1T2nsHx9FZ7Nju4QuQPUffepHeJb/juqVY1T5SVJ8hoDdo7RFHZRhdUf6+LT8s/6tKLjL6H9POMoGQ==", "dev": true}, "node_modules/array-union": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/array-union/-/array-union-0.1.0.tgz", "integrity": "sha512-XVcavaat1j9X9CvRZnqRKlI3MM4+XoDdjrOWytkFncy2Z2hSPYyTEtNBNTyMrX+q7Yw+W/e4kURGCiAH0wEy+w==", "dev": true, "dependencies": {"array-uniq": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/array-uniq": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/array-uniq/-/array-uniq-0.1.1.tgz", "integrity": "sha512-f2yZoxGtRdrmvV142WQ8ALxpA/nQihGiO0kWVkA4O1dmUbCFn3HjYu6wnKzs/gBhB/gJiEKhk66DtSTEVZM5ag==", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/asn1": {"version": "0.1.11", "resolved": "https://registry.npmjs.org/asn1/-/asn1-0.1.11.tgz", "integrity": "sha512-Fh9zh3G2mZ8qM/kwsiKwL2U2FmXxVsboP4x1mXjnhKHv3SmzaBZoYvxEQJz/YS2gnCgd8xlAVWcZnQyC9qZBsA==", "dev": true, "engines": {"node": ">=0.4.9"}}, "node_modules/assert-plus": {"version": "0.1.5", "resolved": "https://registry.npmjs.org/assert-plus/-/assert-plus-0.1.5.tgz", "integrity": "sha512-brU24g7ryhRwGCI2y+1dGQmQXiZF7TtIj583S96y0jjdajIe6wn8BuXyELYhvD22dtIxDQVFk04YTJwwdwOYJw==", "dev": true, "optional": true, "engines": {"node": ">=0.8"}}, "node_modules/async": {"version": "0.1.22", "resolved": "https://registry.npmjs.org/async/-/async-0.1.22.tgz", "integrity": "sha512-2tEzliJmf5fHNafNwQLJXUasGzQCVctvsNkXmnlELHwypU0p08/rHohYvkqKIjyXpx+0rkrYv6QbhJ+UF4QkBg==", "dev": true, "engines": {"node": "*"}}, "node_modules/autoprefixer": {"version": "1.3.1", "resolved": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-1.3.1.tgz", "integrity": "sha512-TUMhFBOjSFVRuxLlU3YNr6ZyagzPPpXbveCk2qSxEoE8GCK1D2k8PpNTF8zFc7S3QosTIZcC1PVm3YOJFcV8WQ==", "dev": true, "dependencies": {"caniuse-db": "1.0.20140618 - 2", "fs-extra": "~0.9.1", "postcss": "~0.3.5"}, "bin": {"autoprefixer": "bin/autoprefixer"}}, "node_modules/aws-sign": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/aws-sign/-/aws-sign-0.3.0.tgz", "integrity": "sha512-pEMJAknifcXqXqYVXzGPIu8mJvxtJxIdpVpAs8HNS+paT+9srRUDMQn+3hULS7WbLmttcmvgMvnDcFujqXJyPw==", "dev": true, "engines": {"node": "*"}}, "node_modules/aws-sign2": {"version": "0.5.0", "resolved": "https://registry.npmjs.org/aws-sign2/-/aws-sign2-0.5.0.tgz", "integrity": "sha512-oqUX0DM5j7aPWPCnpWebiyNIj2wiNI87ZxnOMoGv0aE4TGlBy2N+5iWc6dQ/NOKZaBD2W6PVz8jtOGkWzSC5EA==", "dev": true, "optional": true, "engines": {"node": "*"}}, "node_modules/base64-js": {"version": "0.0.8", "resolved": "https://registry.npmjs.org/base64-js/-/base64-js-0.0.8.tgz", "integrity": "sha512-3XSA2cR/h/73EzlXXdU6YNycmYI7+kicTxks4eJg2g39biHR84slg2+des+p7iHYhbRg/udIS4TD53WabcOUkw==", "dev": true, "engines": {"node": ">= 0.4"}}, "node_modules/basic-auth-connect": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/basic-auth-connect/-/basic-auth-connect-1.0.0.tgz", "integrity": "sha512-kiV+/DTgVro4aZifY/hwRwALBISViL5NP4aReaR2EVJEObpbUBHIkdJh/YpcoEiYt7nBodZ6U2ajZeZvSxUCCg==", "dev": true}, "node_modules/batch": {"version": "0.5.0", "resolved": "https://registry.npmjs.org/batch/-/batch-0.5.0.tgz", "integrity": "sha512-avtDJBSxllB5QGphW1OXYF+ujhy/yIGgeFsvK6UiZLU86nWlqsNcZotUKd001wrl9MmZ9QIyVy8WFVEEpRIc5A==", "dev": true}, "node_modules/bcrypt-pbkdf": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/bcrypt-pbkdf/-/bcrypt-pbkdf-1.0.2.tgz", "integrity": "sha512-qeFIXtP4MSoi6NLqO12WfqARWWuCKi2Rn/9hJLEmtB5yTNr9DqFWkJRCf2qShWzPeAMRnOgCrq0sg/KLv5ES9w==", "dev": true, "dependencies": {"tweetnacl": "^0.14.3"}}, "node_modules/bl": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/bl/-/bl-1.0.3.tgz", "integrity": "sha512-phbvN+yOk05EGoFcV/0S8N8ShnJqf6VCWRAw5he2gvRwBubFt/OzmcTNGqBt5b7Y4RK3YCgf6jrgGSR0Cwtsgw==", "dev": true, "dependencies": {"readable-stream": "~2.0.5"}}, "node_modules/bl/node_modules/isarray": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz", "integrity": "sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ==", "dev": true}, "node_modules/bl/node_modules/process-nextick-args": {"version": "1.0.7", "resolved": "https://registry.npmjs.org/process-nextick-args/-/process-nextick-args-1.0.7.tgz", "integrity": "sha512-yN0WQmuCX63LP/TMvAg31nvT6m4vDqJEiiv2CAZqWOGNWutc9DfDk1NPYYmKUFmaVM2UwDowH4u5AHWYP/jxKw==", "dev": true}, "node_modules/bl/node_modules/readable-stream": {"version": "2.0.6", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.0.6.tgz", "integrity": "sha512-TXcFfb63BQe1+ySzsHZI/5v1aJPCShfqvWJ64ayNImXMsN1Cd0YGk/wm8KB7/OeessgPc9QvS9Zou8QTkFzsLw==", "dev": true, "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "~1.0.0", "process-nextick-args": "~1.0.6", "string_decoder": "~0.10.x", "util-deprecate": "~1.0.1"}}, "node_modules/body-parser": {"version": "1.3.1", "resolved": "https://registry.npmjs.org/body-parser/-/body-parser-1.3.1.tgz", "integrity": "sha512-89/x3K5hk+ZTWDeuT2K0omjSaA8uzT15raeGH65ZktRs+odBKtyjkB2Gw+eIYSqOW9YFDk8wA47y78kVJ/Prlg==", "dev": true, "dependencies": {"bytes": "1.0.0", "qs": "0.6.6", "raw-body": "1.1.6", "type-is": "1.2.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/boom": {"version": "0.4.2", "resolved": "https://registry.npmjs.org/boom/-/boom-0.4.2.tgz", "integrity": "sha512-OvfN8y1oAxxphzkl2SnCS+ztV/uVKTATtgLjWYg/7KwcNyf3rzpHxNQJZCKtsZd4+MteKczhWbSjtEX4bGgU9g==", "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial).", "dev": true, "dependencies": {"hoek": "0.9.x"}, "engines": {"node": ">=0.8.0"}}, "node_modules/bootstrap": {"resolved": "", "link": true}, "node_modules/browserify-zlib": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/browserify-zlib/-/browserify-zlib-0.1.4.tgz", "integrity": "sha512-19OEpq7vWgsH6WkvkBJQDFvJS1uPcbFOQ4v9CU839dO+ZZXUZO6XpE6hNCqvlIIj+4fZvRiJ6DsAQ382GwiyTQ==", "dev": true, "dependencies": {"pako": "~0.2.0"}}, "node_modules/btoa": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/btoa/-/btoa-1.1.2.tgz", "integrity": "sha512-57M3ScafoXWLG+icLeUz41DJkekDPwiBJ0jPZTE0VOyHLW5F2mpJr1x/kXEOICJ6luXobUShqYHop5kD3cZing==", "dev": true, "bin": {"btoa": "bin/btoa.js"}, "engines": {"node": ">= 0.4.0"}}, "node_modules/buffer-crc32": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/buffer-crc32/-/buffer-crc32-0.2.1.tgz", "integrity": "sha512-vMfBIRp/wjlpueSz7Sb0OmO7C5SH58SSmbsT8G4D48YfO/Zgbr29xNXMpZVSC14ujVJfrZZH1Bl+kXYRQPuvfQ==", "dev": true, "engines": {"node": "*"}}, "node_modules/buffer-from": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.2.tgz", "integrity": "sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==", "dev": true}, "node_modules/bytes": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/bytes/-/bytes-1.0.0.tgz", "integrity": "sha512-/x68VkHLeTl3/Ll8IvxdwzhrT+IyKc52e/oyHhA2RwqPqswSnjVbSddfPRwAsJtbilMAPSRWwAlpxdYsSWOTKQ==", "dev": true}, "node_modules/camelcase": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/camelcase/-/camelcase-1.2.1.tgz", "integrity": "sha512-wzLkDa4K/mzI1OSITC+DUyjgIl/ETNHE9QvYgy6J6Jvqyyz4C0Xfd+lQhb19sX2jMpZV4IssUn0VDVmglV+s4g==", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/caniuse-db": {"version": "1.0.30001709", "resolved": "https://registry.npmjs.org/caniuse-db/-/caniuse-db-1.0.30001709.tgz", "integrity": "sha512-WtE+pFmP8dqeAsdTq5+1QWCynRmXi71DkA0VyGDoV/nsdYQgT1oWd4dV+eO1uqzCjZ8C1/P8Wm8/Hwe3vHY2kQ==", "dev": true}, "node_modules/cardinal": {"version": "0.5.0", "resolved": "https://registry.npmjs.org/cardinal/-/cardinal-0.5.0.tgz", "integrity": "sha512-HRGILv/FykuNVO7xCTiXMEPJxA7jMyHwEgljgcAZ3xkLSEhLvStqcTy6xamC1NH4Ja8hlc9VHlXKoSuFbI+7lA==", "dev": true, "dependencies": {"ansicolors": "~0.2.1", "redeyed": "~0.5.0"}, "bin": {"cdl": "bin/cdl.js"}}, "node_modules/caseless": {"version": "0.11.0", "resolved": "https://registry.npmjs.org/caseless/-/caseless-0.11.0.tgz", "integrity": "sha512-ODLXH644w9C2fMPAm7bMDQ3GRvipZWZfKc+8As6hIadRIelE0n0xZuN38NS6kiK3KPEVrpymmQD8bvncAHWQkQ==", "dev": true}, "node_modules/center-align": {"version": "0.1.3", "resolved": "https://registry.npmjs.org/center-align/-/center-align-0.1.3.tgz", "integrity": "sha512-Baz3aNe2gd2LP2qk5U+sDk/m4oSuwSDcBfayTCTBoWpfIGO5XFxPmjILQII4NGiZjD6DoDI6kf7gKaxkf7s3VQ==", "dev": true, "dependencies": {"align-text": "^0.1.3", "lazy-cache": "^1.0.3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/chalk": {"version": "0.4.0", "resolved": "https://registry.npmjs.org/chalk/-/chalk-0.4.0.tgz", "integrity": "sha512-sQfYDlfv2DGVtjdoQqxS0cEZDroyG8h6TamA6rvxwlrU5BaSLDx9xhatBYl2pxZ7gmpNaPFVwBtdGdu5rQ+tYQ==", "dev": true, "dependencies": {"ansi-styles": "~1.0.0", "has-color": "~0.1.0", "strip-ansi": "~0.1.0"}, "engines": {"node": ">=0.8.0"}}, "node_modules/character-parser": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/character-parser/-/character-parser-1.2.0.tgz", "integrity": "sha512-FtpaoJWMk2JsI8h/Qhc5cLQMMT8cbqxDJ/vP2t5a1TcjnSPHGj0dcovj5H6lR5gzMAgJbe5dlIfbx1rGufvHgw==", "dev": true}, "node_modules/clean-css": {"version": "2.2.23", "resolved": "https://registry.npmjs.org/clean-css/-/clean-css-2.2.23.tgz", "integrity": "sha512-4Ft5U6dNFqr++uztZJzaGXLTTI1aGYgOVAvGAwjp1eOPd4jT6HKjYE2CJ6qnAWZ3/H/U77iFeCZyhhBmOUaU1w==", "dev": true, "dependencies": {"commander": "2.2.x"}, "bin": {"cleancss": "bin/cleancss"}, "engines": {"node": ">=0.8.0"}}, "node_modules/cli": {"version": "0.6.6", "resolved": "https://registry.npmjs.org/cli/-/cli-0.6.6.tgz", "integrity": "sha512-4H6IzYk78R+VBeJ3fH3VQejcQRkGPR+kMjA9n30srEN+YVMPJLHfoQDtLquIzcLnfrlUrVA8qSQRB9fdgWpUBw==", "dev": true, "dependencies": {"exit": "0.1.2", "glob": "~ 3.2.1"}, "engines": {"node": ">=0.2.5"}}, "node_modules/cli-color": {"version": "0.1.7", "resolved": "https://registry.npmjs.org/cli-color/-/cli-color-0.1.7.tgz", "integrity": "sha512-xNaQxWYgI6DD4xIJLn8GY2zDZVbrN0vsU1fEbDNAHZRyceWhpj7A08mYcG1AY92q1Aw0geYkVfiAcEYIZtuTSg==", "dev": true, "dependencies": {"es5-ext": "0.8.x"}, "engines": {"node": ">=0.1.103"}}, "node_modules/cli/node_modules/glob": {"version": "3.2.11", "resolved": "https://registry.npmjs.org/glob/-/glob-3.2.11.tgz", "integrity": "sha512-hVb0zwEZwC1FXSKRPFTeOtN7AArJcJlI6ULGLtrstaswKNlrTJqAA+1lYlSUop4vjA423xlBzqfVS3iWGlqJ+g==", "deprecated": "Glob versions prior to v9 are no longer supported", "dev": true, "dependencies": {"inherits": "2", "minimatch": "0.3"}, "engines": {"node": "*"}}, "node_modules/cli/node_modules/minimatch": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-0.3.0.tgz", "integrity": "sha512-WFX1jI1AaxNTZVOHLBVazwTWKaQjoykSzCBNXB72vDTCzopQGtyP91tKdFK5cv1+qMwPyiTu1HqUriqplI8pcA==", "deprecated": "Please update to minimatch 3.0.2 or higher to avoid a RegExp DoS issue", "dev": true, "dependencies": {"lru-cache": "2", "sigmund": "~1.0.0"}, "engines": {"node": "*"}}, "node_modules/cliui": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/cliui/-/cliui-2.1.0.tgz", "integrity": "sha512-GIOYRizG+TGoc7Wgc1LiOTLare95R3mzKgoln+Q/lE4ceiYH19gUpl0l0Ffq4lJDEf3FxujMe6IBfOCs7pfqNA==", "dev": true, "dependencies": {"center-align": "^0.1.1", "right-align": "^0.1.1", "wordwrap": "0.0.2"}}, "node_modules/cliui/node_modules/wordwrap": {"version": "0.0.2", "resolved": "https://registry.npmjs.org/wordwrap/-/wordwrap-0.0.2.tgz", "integrity": "sha512-xSBsCeh+g+dinoBv3GAOWM4LcVVO68wLXRanibtBSdUvkGWQRGeE9P7IwU9EmDDi4jA6L44lz15CGMwdw9N5+Q==", "dev": true, "engines": {"node": ">=0.4.0"}}, "node_modules/coffee-script": {"version": "1.3.3", "resolved": "https://registry.npmjs.org/coffee-script/-/coffee-script-1.3.3.tgz", "integrity": "sha512-QjQ1T4BqyHv19k6XSfdhy/QLlIOhywz0ekBUCa9h71zYMJlfDTGan/Z1JXzYkZ6v8R+GhvL/p4FZPbPW8WNXlg==", "deprecated": "CoffeeScript on NPM has moved to \"coffeescript\" (no hyphen)", "dev": true, "bin": {"cake": "bin/cake", "coffee": "bin/coffee"}, "engines": {"node": ">=0.4.0"}}, "node_modules/colors": {"version": "0.6.2", "resolved": "https://registry.npmjs.org/colors/-/colors-0.6.2.tgz", "integrity": "sha512-OsSVtHK8Ir8r3+Fxw/b4jS1ZLPXkV6ZxDRJQzeD7qo0SqMXWrHDM71DgYzPMHY8SFJ0Ao+nNU2p1MmwdzKqPrw==", "dev": true, "engines": {"node": ">=0.1.90"}}, "node_modules/combined-stream": {"version": "0.0.7", "resolved": "https://registry.npmjs.org/combined-stream/-/combined-stream-0.0.7.tgz", "integrity": "sha512-qfexlmLp9MyrkajQVyjEDb0Vj+KhRgR/rxLiVhaihlT+ZkX0lReqtH6Ack40CvMDERR4b5eFp3CreskpBs1Pig==", "dev": true, "dependencies": {"delayed-stream": "0.0.5"}, "engines": {"node": ">= 0.8"}}, "node_modules/commander": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/commander/-/commander-2.2.0.tgz", "integrity": "sha512-U6hBkeIsoeE81B+yas9uVF4YYVcVoBCwb1e314VPyvVQubFwvnTAuc1oUQ6VuMPYUS4Rf1gzr0wTVLvs4sb5Pw==", "dev": true, "engines": {"node": ">= 0.6.x"}}, "node_modules/compressible": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/compressible/-/compressible-1.1.0.tgz", "integrity": "sha512-rCwUIlpYk3MyJwPuNJUFY4GkusYq33phMUj0iuJxpmRa7FVyFyTy4O4S2DxheA8LBWZcd3ZiotCR9GZE2PLyzQ==", "dev": true}, "node_modules/compression": {"version": "1.0.7", "resolved": "https://registry.npmjs.org/compression/-/compression-1.0.7.tgz", "integrity": "sha512-358POVi/83+vOraY0hLNi1s/7G7e3MiZKVlrYiu422gWWjI1AKBXa4sQXnoYYLAyB29jUdo8bqFL7C4JX6kBnA==", "dev": true, "dependencies": {"accepts": "1.0.3", "bytes": "1.0.0", "compressible": "1.1.0", "on-headers": "0.0.0", "vary": "0.1.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/concat-stream": {"version": "1.6.2", "resolved": "https://registry.npmjs.org/concat-stream/-/concat-stream-1.6.2.tgz", "integrity": "sha512-27HBghJxjiZtIk3Ycvn/4kbJk/1uZuJFfuPEns6LaEvpvG1f0hTea8lilrouyo9mVc2GWdcEZ8OLoGmSADlrCw==", "dev": true, "engines": ["node >= 0.8"], "dependencies": {"buffer-from": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^2.2.2", "typedarray": "^0.0.6"}}, "node_modules/concat-stream/node_modules/isarray": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz", "integrity": "sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ==", "dev": true}, "node_modules/concat-stream/node_modules/readable-stream": {"version": "2.3.8", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.8.tgz", "integrity": "sha512-8p0AUk4XODgIewSi0l8Epjs+EVnWiK7NoDIEGU0HhE7+ZyY8D1IMY7odu5lRrFXGg71L15KG8QrPmum45RTtdA==", "dev": true, "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "node_modules/concat-stream/node_modules/string_decoder": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-1.1.1.tgz", "integrity": "sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==", "dev": true, "dependencies": {"safe-buffer": "~5.1.0"}}, "node_modules/connect": {"version": "2.19.6", "resolved": "https://registry.npmjs.org/connect/-/connect-2.19.6.tgz", "integrity": "sha512-1w2SKHo+5+CVn3XHFJXFNvb+s8hhYhEPhdsxsqt5wk41VfN95bB4vkSdMSLQW50jU2zEtFh2mvduoH15FJ+1rw==", "deprecated": "connect 2.x series is deprecated", "dev": true, "dependencies": {"basic-auth-connect": "1.0.0", "body-parser": "1.3.1", "bytes": "1.0.0", "compression": "1.0.7", "connect-timeout": "1.1.0", "cookie": "0.1.2", "cookie-parser": "1.1.0", "cookie-signature": "1.0.3", "csurf": "1.2.1", "debug": "1.0.2", "errorhandler": "1.0.2", "escape-html": "1.0.1", "express-session": "1.2.1", "fresh": "0.2.2", "method-override": "2.0.2", "morgan": "1.1.1", "multiparty": "3.2.8", "on-headers": "0.0.0", "parseurl": "1.0.1", "pause": "0.0.1", "qs": "0.6.6", "response-time": "2.0.0", "serve-favicon": "2.0.1", "serve-index": "1.1.1", "serve-static": "1.2.3", "type-is": "1.2.1", "vhost": "1.0.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/connect-livereload": {"version": "0.4.1", "resolved": "https://registry.npmjs.org/connect-livereload/-/connect-livereload-0.4.1.tgz", "integrity": "sha512-y/4fDdcveHQsVXTJeAr8HHLwul5M3VI071pEaov/jiCz2HIrmZynFHuOn1CfQ8euUsdjR1Ppwsb9mgBOSij3vw==", "dev": true, "engines": {"node": "*"}}, "node_modules/connect-timeout": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/connect-timeout/-/connect-timeout-1.1.0.tgz", "integrity": "sha512-8J5tx50FQBnFpLWIkuzpiq0/K+9Q38VtPUJb5cwZ9OcNQwlFsXunfUG/HjQeAKYEwzXBMTYX3vECP3NrltHQSw==", "dev": true, "dependencies": {"debug": "0.8.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/connect-timeout/node_modules/debug": {"version": "0.8.1", "resolved": "https://registry.npmjs.org/debug/-/debug-0.8.1.tgz", "integrity": "sha512-HlXEJm99YsRjLJ8xmuz0Lq8YUwrv7hAJkTEr6/Em3sUlSUNl0UdFA+1SrY4fnykeq1FVkUEUtwRGHs9VvlYbGA==", "dev": true, "engines": {"node": "*"}}, "node_modules/console-browserify": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/console-browserify/-/console-browserify-1.1.0.tgz", "integrity": "sha512-duS7VP5pvfsNLDvL1O4VOEbw37AI3A4ZUQYemvDlnpGrNu9tprR7BYWpDYwC0Xia0Zxz5ZupdiIrUp0GH1aXfg==", "dev": true, "dependencies": {"date-now": "^0.1.4"}}, "node_modules/constantinople": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/constantinople/-/constantinople-2.0.1.tgz", "integrity": "sha512-ZF+ejGc/Gu8ucEaiTOgbptlCxKnzHtUaS7rwB/ZYjnoCZxZyuu82V6zQB/Ax/CS/rp/x2FI/x8gQxrtC4ekxlw==", "deprecated": "Please update to at least constantinople 3.1.1", "dev": true, "dependencies": {"uglify-js": "~2.4.0"}}, "node_modules/constantinople/node_modules/async": {"version": "0.2.10", "resolved": "https://registry.npmjs.org/async/-/async-0.2.10.tgz", "integrity": "sha512-eAkdoKxU6/LkKDBzLpT+t6Ff5EtfSF4wx1WfJiPEEV7WNLnDaRXk0oVysiEPm262roaachGexwUv94WhSgN5TQ==", "dev": true}, "node_modules/constantinople/node_modules/source-map": {"version": "0.1.34", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.1.34.tgz", "integrity": "sha512-yfCwDj0vR9RTwt3pEzglgb3ZgmcXHt6DjG3bjJvzPwTL+5zDQ2MhmSzAcTy0GTiQuCiriSWXvWM1/NhKdXuoQA==", "dev": true, "dependencies": {"amdefine": ">=0.0.4"}, "engines": {"node": ">=0.8.0"}}, "node_modules/constantinople/node_modules/uglify-js": {"version": "2.4.24", "resolved": "https://registry.npmjs.org/uglify-js/-/uglify-js-2.4.24.tgz", "integrity": "sha512-tktIjwackfZLd893KGJmXc1hrRHH1vH9Po3xFh1XBjjeGAnN02xJ3SuoA+n1L29/ZaCA18KzCFlckS+vfPugiA==", "dev": true, "dependencies": {"async": "~0.2.6", "source-map": "0.1.34", "uglify-to-browserify": "~1.0.0", "yargs": "~3.5.4"}, "bin": {"uglifyjs": "bin/uglifyjs"}, "engines": {"node": ">=0.4.0"}}, "node_modules/constantinople/node_modules/wordwrap": {"version": "0.0.2", "resolved": "https://registry.npmjs.org/wordwrap/-/wordwrap-0.0.2.tgz", "integrity": "sha512-xSBsCeh+g+dinoBv3GAOWM4LcVVO68wLXRanibtBSdUvkGWQRGeE9P7IwU9EmDDi4jA6L44lz15CGMwdw9N5+Q==", "dev": true, "engines": {"node": ">=0.4.0"}}, "node_modules/constantinople/node_modules/yargs": {"version": "3.5.4", "resolved": "https://registry.npmjs.org/yargs/-/yargs-3.5.4.tgz", "integrity": "sha512-5j382E4xQSs71p/xZQsU1PtRA2HXPAjX0E0DkoGLxwNASMOKX6A9doV1NrZmj85u2Pjquz402qonBzz/yLPbPA==", "dev": true, "dependencies": {"camelcase": "^1.0.2", "decamelize": "^1.0.0", "window-size": "0.1.0", "wordwrap": "0.0.2"}}, "node_modules/cookie": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/cookie/-/cookie-0.1.2.tgz", "integrity": "sha512-+mHmWbhevLwkiBf7QcbZXHr0v4ZQQ/OgHk3fsQHrsMMiGzuvAmU/YMUR+ZfrO/BLAGIWFfx2Z7Oyso0tZR/wiA==", "dev": true, "engines": {"node": "*"}}, "node_modules/cookie-jar": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/cookie-jar/-/cookie-jar-0.3.0.tgz", "integrity": "sha512-dX1400pzPULr+ZovkIsDEqe7XH8xCAYGT5Dege4Eot44Qs2mS2iJmnh45TxTO5MIsCfrV/JGZVloLhm46AHxNw==", "dev": true, "engines": {"node": "*"}}, "node_modules/cookie-parser": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/cookie-parser/-/cookie-parser-1.1.0.tgz", "integrity": "sha512-MsTYnJLvYrlVn2vyjqxYch2KSQVxGmE6j5QouzSMmqS2VEQgrQqelPLqIhaqRKRA3pDaDuZklLhamfwEFFP9BQ==", "dev": true, "dependencies": {"cookie": "0.1.2", "cookie-signature": "1.0.3"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/cookie-signature": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/cookie-signature/-/cookie-signature-1.0.3.tgz", "integrity": "sha512-/KzKzsm0OlguYov01OlOpTkX5MhBKUmfL/KMum7R80rPKheb9AwUzr78TwtBt1OdbnWrt4X+wxbTfcQ3noZqHw==", "dev": true}, "node_modules/cookiejar": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/cookiejar/-/cookiejar-1.3.0.tgz", "integrity": "sha512-U+NgxxtwHIokuL04FqKEkqsaWBDtnCQo+wvYjUCtBA56Lcg8vpV3SGtBx+RAmw92SV3VT8PwsYcCFK/cC3Dw+A==", "dev": true, "engines": {"node": "*"}}, "node_modules/core-util-is": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/core-util-is/-/core-util-is-1.0.3.tgz", "integrity": "sha512-ZQBvi1DcpJ4GDqanjucZ2Hj3wEO5pZDS89BWbkcrvdxksJorwUDDZamX9ldFkp9aw2lmBDLgkObEA4DWNJ9FYQ==", "dev": true}, "node_modules/cryptiles": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/cryptiles/-/cryptiles-0.2.2.tgz", "integrity": "sha512-gvWSbgqP+569DdslUiCelxIv3IYK5Lgmq1UrRnk+s1WxQOQ16j3GPDcjdtgL5Au65DU/xQi6q3xPtf5Kta+3IQ==", "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial).", "dev": true, "dependencies": {"boom": "0.4.x"}, "engines": {"node": ">=0.8.0"}}, "node_modules/csrf-tokens": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/csrf-tokens/-/csrf-tokens-1.0.4.tgz", "integrity": "sha512-E1wOfXYkqlUAdwwWcFk3QBmE7h9J5/s6WBJt4LQx4zeEb+oybx/UDTTwsqF/5R5G0HXejfl7iE3nmjjzUPyyyQ==", "dev": true, "dependencies": {"rndm": "1", "scmp": "~0.0.3", "uid2": "~0.0.2"}}, "node_modules/css": {"version": "1.0.8", "resolved": "https://registry.npmjs.org/css/-/css-1.0.8.tgz", "integrity": "sha512-qmTYWhHk910nQWnGqMAiWWPQlB6tESiWgNebQJmiozOAGcBAQ1+U/UzUOkhdrcshlkSRRiKWodwmVvO0OmnIGg==", "dev": true, "dependencies": {"css-parse": "1.0.4", "css-stringify": "1.0.5"}}, "node_modules/css-parse": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/css-parse/-/css-parse-1.0.4.tgz", "integrity": "sha512-pfstzKVRZiHprDXdsmtfH1HYUEw22lzjuHdnpe1hscwoQvgW2C5zDQIBE0RKoALEReTn9W1ECdY8uaT/kO4VfA==", "dev": true}, "node_modules/css-stringify": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/css-stringify/-/css-stringify-1.0.5.tgz", "integrity": "sha512-aIThpcErhG5EyHorGqNlTh0TduNBqLrrXLO3x5rku3ZKBxuVfY+T7noyM2G2X/01iQANqJUb6d3+FLoa+N7Xwg==", "dev": true}, "node_modules/csscomb": {"version": "2.0.5", "resolved": "https://registry.npmjs.org/csscomb/-/csscomb-2.0.5.tgz", "integrity": "sha512-HhQtPpaS2pJziy2aQtTGoWUfaxu3r31/aLbOqyWGqD5zeyfBO2D70YYFu4IaRdxBeIG78FbLYYZHoPWuHLDW9A==", "dev": true, "dependencies": {"commander": "2.0.0", "gonzales-pe": "2.0.x", "minimatch": "0.2.12", "vow": "0.4.4", "vow-fs": "0.3.2"}, "bin": {"csscomb": "bin/csscomb"}, "engines": {"node": ">= 0.10.0"}}, "node_modules/csscomb/node_modules/commander": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/commander/-/commander-2.0.0.tgz", "integrity": "sha512-qebjpyeaA/nJ4w3EO2cV2++/zEkccPnjWogzA2rff+Lk8ILI75vULeTmyd4wPxWdKwtP3J+G39IXVZadh0UHyw==", "dev": true, "engines": {"node": ">= 0.6.x"}}, "node_modules/csscomb/node_modules/minimatch": {"version": "0.2.12", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-0.2.12.tgz", "integrity": "sha512-jeVdfKmlomLerf8ecetSr6gLS0OXnLRluhnv9Rf2yj70NsD8uVGqrpwTqJGKpIF8VTRR9fQAl62CZ1eNIEMk3A==", "deprecated": "Please update to minimatch 3.0.2 or higher to avoid a RegExp DoS issue", "dev": true, "dependencies": {"lru-cache": "2", "sigmund": "~1.0.0"}, "engines": {"node": "*"}}, "node_modules/csslint": {"version": "0.10.0", "resolved": "https://registry.npmjs.org/csslint/-/csslint-0.10.0.tgz", "integrity": "sha512-mlD1oDw0juzD4dOthyAytPC4NsXqVZeIYAScIbgoYGY+Q7vcrhOQrH7js4JVZXcrOyKxi8ytC42ENMwO9CdnMQ==", "dev": true, "dependencies": {"parserlib": "~0.2.2"}, "bin": {"csslint": "cli.js"}, "engines": {"node": ">=0.8.0"}}, "node_modules/csurf": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/csurf/-/csurf-1.2.1.tgz", "integrity": "sha512-6UpuNy/81/YM2pK0nOiQoqvs8az+c2W7MiT1ld7Fn1xpANWJucVcBaAK6cP8N4K34kQx5AGgLlUiO3r5Ckcghg==", "dev": true, "dependencies": {"csrf-tokens": "~1.0.2"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/ctype": {"version": "0.5.3", "resolved": "https://registry.npmjs.org/ctype/-/ctype-0.5.3.tgz", "integrity": "sha512-T6CEkoSV4q50zW3TlTHMbzy1E5+zlnNcY+yb7tWVYlTwPhx9LpnfAkd4wecpWknDyptp4k97LUZeInlf6jdzBg==", "dev": true, "optional": true, "engines": {"node": ">= 0.4"}}, "node_modules/dashdash": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/dashdash/-/dashdash-1.14.1.tgz", "integrity": "sha512-jRFi8UDGo6j+odZiEpjazZaWqEal3w/basFjQHQEwVtZJGDpxbH1MeYluwCS8Xq5wmLJooDlMgvVarmWfGM44g==", "dev": true, "dependencies": {"assert-plus": "^1.0.0"}, "engines": {"node": ">=0.10"}}, "node_modules/dashdash/node_modules/assert-plus": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/assert-plus/-/assert-plus-1.0.0.tgz", "integrity": "sha512-NfJ4UzBCcQGLDlQq7nHxH+tv3kyZ0hHQqF5BO6J7tNJeP5do1llPr8dZ8zHonfhAu0PHAdMkSo+8o0wxg9lZWw==", "dev": true, "engines": {"node": ">=0.8"}}, "node_modules/date-now": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/date-now/-/date-now-0.1.4.tgz", "integrity": "sha512-AsElvov3LoNB7tf5k37H2jYSB+ZZPMT5sG2QjJCcdlV5chIv6htBUBUui2IKRjgtKAKtCBN7Zbwa+MtwLjSeNw==", "dev": true}, "node_modules/date-time": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/date-time/-/date-time-0.1.1.tgz", "integrity": "sha512-p4psdkgdNA6x0600SKbfWiOomNb33ADBMRHf49GMhYVgJsPefZlMSLXXVWWUpbqSxB3DL5/cxKa6a8i3XPK5Xg==", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/dateformat": {"version": "1.0.2-1.2.3", "resolved": "https://registry.npmjs.org/dateformat/-/dateformat-1.0.2-1.2.3.tgz", "integrity": "sha512-AXvW8g7tO4ilk5HgOWeDmPi/ZPaCnMJ+9Cg1I3p19w6mcvAAXBuuGEXAxybC+Djj1PSZUiHUcyoYu7WneCX8gQ==", "dev": true, "engines": {"node": "*"}}, "node_modules/debug": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/debug/-/debug-1.0.2.tgz", "integrity": "sha512-T9bufXIzQvCa4VrTIpLvvwdLhH+wuBtvIJJA3xgzVcaVETGmTIWMfEXQEd1K4p8BaRmQJPn6MPut38H7YQ+iIA==", "dev": true, "dependencies": {"ms": "0.6.2"}}, "node_modules/decamelize": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/decamelize/-/decamelize-1.2.0.tgz", "integrity": "sha512-z2S+W9X73hAUUki+N+9Za2lBlun89zigOyGrsax+KUQ6wKW4ZoWpEYBkGhQjwAjjDCkWxhY0VKEhk8wzY7F5cA==", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/deep-equal": {"version": "0.0.0", "resolved": "https://registry.npmjs.org/deep-equal/-/deep-equal-0.0.0.tgz", "integrity": "sha512-p1bI/kkDPT6auUI0U+WLuIIrzmDIDo80I406J8tT4y6I4ZGtBuMeTudrKDtBdMJFAcxqrQdx27gosqPVyY3IvQ==", "dev": true, "engines": {"node": "*"}}, "node_modules/defined": {"version": "0.0.0", "resolved": "https://registry.npmjs.org/defined/-/defined-0.0.0.tgz", "integrity": "sha512-zpqiCT8bODLu3QSmLLic8xJnYWBFjOSu/fBCm189oAiTtPq/PSanNACKZDS7kgSyCJY7P+IcODzlIogBK/9RBg==", "dev": true}, "node_modules/delayed-stream": {"version": "0.0.5", "resolved": "https://registry.npmjs.org/delayed-stream/-/delayed-stream-0.0.5.tgz", "integrity": "sha512-v+7uBd1pqe5YtgPacIIbZ8HuHeLFVNe4mUEyFDXL6KiqzEykjbw+5mXZXpGFgNVasdL4jWKgaKIXrEHiynN1LA==", "dev": true, "engines": {"node": ">=0.4.0"}}, "node_modules/diff": {"version": "1.0.8", "resolved": "https://registry.npmjs.org/diff/-/diff-1.0.8.tgz", "integrity": "sha512-1zEb73vemXFpUmfh3fsta4YHz3lwebxXvaWmPbFv9apujQBWDnkrPDLXLQs1gZo4RCWMDsT89r0Pf/z8/02TGA==", "dev": true, "engines": {"node": ">=0.3.1"}}, "node_modules/difflib": {"version": "0.2.4", "resolved": "https://registry.npmjs.org/difflib/-/difflib-0.2.4.tgz", "integrity": "sha512-9YVwmMb0wQHQNr5J9m6BSj6fk4pfGITGQOOs+D9Fl+INODWFOfvhIU1hNv6GgR1RBoC/9NJcwu77zShxV0kT7w==", "dev": true, "dependencies": {"heap": ">= 0.2.0"}, "engines": {"node": "*"}}, "node_modules/dom-serializer": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/dom-serializer/-/dom-serializer-0.2.2.tgz", "integrity": "sha512-2/xPb3ORsQ42nHYiSunXkDjPLBaEj/xTwUO4B7XCZQTRk7EBtTOPaygh10YAAh2OI1Qrp6NWfpAhzswj0ydt9g==", "dev": true, "dependencies": {"domelementtype": "^2.0.1", "entities": "^2.0.0"}}, "node_modules/dom-serializer/node_modules/domelementtype": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/domelementtype/-/domelementtype-2.3.0.tgz", "integrity": "sha512-OLETBj6w0OsagBwdXnPdN0cnMfF9opN69co+7ZrbfPGrdpPVNBUj02spi6B1N7wChLQiPn4CSH/zJvXw56gmHw==", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/fb55"}]}, "node_modules/dom-serializer/node_modules/entities": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/entities/-/entities-2.2.0.tgz", "integrity": "sha512-p92if5Nz619I0w+akJrLZH0MX0Pb5DX39XOwQTtXSdQQOaYH03S1uIQp4mhOZtAXrxq4ViO67YTiLBo2638o9A==", "dev": true, "funding": {"url": "https://github.com/fb55/entities?sponsor=1"}}, "node_modules/domelementtype": {"version": "1.3.1", "resolved": "https://registry.npmjs.org/domelementtype/-/domelementtype-1.3.1.tgz", "integrity": "sha512-BSKB+TSpMpFI/HOxCNr1O8aMOTZ8hT3pM3GQ0w/mWRmkhEDSFJkkyzz4XQsBV44BChwGkrDfMyjVD0eA2aFV3w==", "dev": true}, "node_modules/domhandler": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/domhandler/-/domhandler-2.3.0.tgz", "integrity": "sha512-q9bUwjfp7Eif8jWxxxPSykdRZAb6GkguBGSgvvCrhI9wB71W2K/Kvv4E61CF/mcCfnVJDeDWx/Vb/uAqbDj6UQ==", "dev": true, "dependencies": {"domelementtype": "1"}}, "node_modules/domutils": {"version": "1.5.1", "resolved": "https://registry.npmjs.org/domutils/-/domutils-1.5.1.tgz", "integrity": "sha512-gSu5Oi/I+3wDENBsOWBiRK1eoGxcywYSqg3rR960/+EfY0CF4EX1VPkgHOZ3WiS/Jg2DtliF6BhWcHlfpYUcGw==", "dev": true, "dependencies": {"dom-serializer": "0", "domelementtype": "1"}}, "node_modules/dreamopt": {"version": "0.6.0", "resolved": "https://registry.npmjs.org/dreamopt/-/dreamopt-0.6.0.tgz", "integrity": "sha512-<PERSON><PERSON><PERSON>a47iBEK0y6ZtgCgy2ykuvMT8c9gj3ua9Dv7vCkclFJJeH2FjhGY2xO5qBoWGahsjCGMlk4Cq9wJYeWxuYhQ==", "dev": true, "dependencies": {"wordwrap": ">=0.0.2"}, "engines": {"node": ">=0.4.0"}}, "node_modules/ecc-jsbn": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/ecc-jsbn/-/ecc-jsbn-0.1.2.tgz", "integrity": "sha512-eh9O+hwRHNbG4BLTjEl3nw044CkGm5X6LoaCf7LPp7UU8Qrt47JYNi6nPX8xjW97TKGKm1ouctg0QSpZe9qrnw==", "dev": true, "dependencies": {"jsbn": "~0.1.0", "safer-buffer": "^2.1.0"}}, "node_modules/ee-first": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/ee-first/-/ee-first-1.0.3.tgz", "integrity": "sha512-1q/3kz+ZwmrrWpJcCCrBZ3JnBzB1BMA5EVW9nxnIP1LxDZ16Cqs9VdolqLWlExet1vU+bar3WSkAa4/YrA9bIw==", "dev": true}, "node_modules/emitter-component": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/emitter-component/-/emitter-component-1.0.0.tgz", "integrity": "sha512-GZrLdp4Z7OERecoYQYElVVqf6/gcbGUs8nvaE+nmu2dGy453lLgGyPLNX9DdSyojdMqI86fCT9XQqsWJymciEw==", "dev": true}, "node_modules/entities": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/entities/-/entities-1.0.0.tgz", "integrity": "sha512-LbLqfXgJMmy81t+7c14mnulFHJ170cM6E+0vMXR9k/ZiZwgX8i5pNgjTCX3SO4VeUsFLV+8InixoretwU+MjBQ==", "dev": true}, "node_modules/error": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/error/-/error-3.0.0.tgz", "integrity": "sha512-dv4mBdBFVJN+WFQai8QCCnM9zTnfOZuXSG8A3KbrDC0TJt1sNb8NTSn961yY8Qp8L2hwcdUcOvUiiKKwILyAlQ==", "dev": true, "dependencies": {"string-template": "~0.1.3", "xtend": "~2.1.1"}}, "node_modules/errorhandler": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/errorhandler/-/errorhandler-1.0.2.tgz", "integrity": "sha512-HjhQKrzO/diObg+aPZL9IPiUze66xaLq9/unTj4E0v5RgbMcixEnesPksu4lZ7R93V51UgvhLtGvToFnRHKbZA==", "dev": true, "engines": {"node": ">= 0.8"}}, "node_modules/es5-ext": {"version": "0.8.2", "resolved": "https://registry.npmjs.org/es5-ext/-/es5-ext-0.8.2.tgz", "integrity": "sha512-<PERSON><PERSON>om<PERSON>hnKiBdjHR1DPHvf5RHgHPmJaY9JNzFGbMbPgdsUkvnUCN1Ke8J4Y0IMyTwFM2M9l4h2GoHwzwpSmXbA==", "dev": true, "engines": {"node": ">=0.4"}}, "node_modules/escape-html": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/escape-html/-/escape-html-1.0.1.tgz", "integrity": "sha512-z6kAnok8fqVTra7Yu77dZF2Y6ETJlxH58wN38wNyuNQLm8xXdKnfNrlSmfXsTePWP03rRVUKHubtUwanwUi7+g==", "dev": true}, "node_modules/escape-string-regexp": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz", "integrity": "sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg==", "dev": true, "engines": {"node": ">=0.8.0"}}, "node_modules/esprima": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/esprima/-/esprima-1.0.4.tgz", "integrity": "sha512-rp5dMKN8zEs9dfi9g0X1ClLmV//WRyk/R15mppFNICIFRG5P92VP7Z04p8pk++gABo9W2tY+kHyu6P1mEHgmTA==", "dev": true, "bin": {"esparse": "bin/esparse.js", "esvalidate": "bin/esvalidate.js"}, "engines": {"node": ">=0.4.0"}}, "node_modules/esprima-fb": {"version": "12001.1.0-dev-harmony-fb", "resolved": "https://registry.npmjs.org/esprima-fb/-/esprima-fb-12001.1.0-dev-harmony-fb.tgz", "integrity": "sha512-kj/HWGgQXRPKf7qAd8Woo2AaxRdT8kpTK2x1+c3zeucEYiIsGktmMC1N3q8J99R0ECMG7Ey/kywsR3qXPHBaXA==", "dev": true, "bin": {"esparse": "bin/esparse.js", "esvalidate": "bin/esvalidate.js"}, "engines": {"node": ">=0.4.0"}}, "node_modules/eventemitter2": {"version": "0.4.14", "resolved": "https://registry.npmjs.org/eventemitter2/-/eventemitter2-0.4.14.tgz", "integrity": "sha512-K7J4xq5xAD5jHsGM5ReWXRTFa3JRGofHiMcVgQ8PRwgWxzjHpMWCIzsmyf60+mh8KLsqYPcjUMa0AC4hd6lPyQ==", "dev": true}, "node_modules/exit": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/exit/-/exit-0.1.2.tgz", "integrity": "sha512-Zk/eNKV2zbjpKzrsQ+n1G6poVbErQxJ0LBOJXaKZ1EViLzH+hrLu9cdXI4zw9dBQJslwBEpbQ2P1oS7nDxs6jQ==", "dev": true, "engines": {"node": ">= 0.8.0"}}, "node_modules/express-session": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/express-session/-/express-session-1.2.1.tgz", "integrity": "sha512-s13UHp8mBqa1dPZaGN86r83AV1UVJor9Z769dJJjgbbIJTJWQVa/FkcTJOPzJhlomfGGdBY/SAX7YJAOGP4ogQ==", "dev": true, "dependencies": {"buffer-crc32": "0.2.1", "cookie": "0.1.2", "cookie-signature": "1.0.3", "debug": "0.8.1", "on-headers": "0.0.0", "uid2": "0.0.3", "utils-merge": "1.0.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/express-session/node_modules/debug": {"version": "0.8.1", "resolved": "https://registry.npmjs.org/debug/-/debug-0.8.1.tgz", "integrity": "sha512-HlXEJm99YsRjLJ8xmuz0Lq8YUwrv7hAJkTEr6/Em3sUlSUNl0UdFA+1SrY4fnykeq1FVkUEUtwRGHs9VvlYbGA==", "dev": true, "engines": {"node": "*"}}, "node_modules/express-session/node_modules/uid2": {"version": "0.0.3", "resolved": "https://registry.npmjs.org/uid2/-/uid2-0.0.3.tgz", "integrity": "sha512-5gSP1liv10Gjp8cMEnFd6shzkL/D6W1uhXSFNCxDC+YI8+L8wkCYCbJ7n77Ezb4wE/xzMogecE+DtamEe9PZjg==", "dev": true}, "node_modules/extend": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/extend/-/extend-3.0.2.tgz", "integrity": "sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g==", "dev": true}, "node_modules/extract-zip": {"version": "1.5.0", "resolved": "https://registry.npmjs.org/extract-zip/-/extract-zip-1.5.0.tgz", "integrity": "sha512-Ht7oUiEXWnX5BvLzMX/UBNIjrAs53lhXtNxMNeUe8Nv0S8rfy5UGqsKOXpP8ZQMWLvheOvRqYYShBoj6fTO9bg==", "dev": true, "dependencies": {"concat-stream": "1.5.0", "debug": "0.7.4", "mkdirp": "0.5.0", "yauzl": "2.4.1"}, "bin": {"extract-zip": "cli.js"}}, "node_modules/extract-zip/node_modules/concat-stream": {"version": "1.5.0", "resolved": "https://registry.npmjs.org/concat-stream/-/concat-stream-1.5.0.tgz", "integrity": "sha512-litEocitzYgqQ0IPaoLw+tCHcVcJJYW05+SAhH+LS9qutSC7iuejvawts3cUYQycZbRbLsjG8mCJLQi2KX5kEw==", "dev": true, "engines": ["node >= 0.8"], "dependencies": {"inherits": "~2.0.1", "readable-stream": "~2.0.0", "typedarray": "~0.0.5"}}, "node_modules/extract-zip/node_modules/debug": {"version": "0.7.4", "resolved": "https://registry.npmjs.org/debug/-/debug-0.7.4.tgz", "integrity": "sha512-EohAb3+DSHSGx8carOSKJe8G0ayV5/i609OD0J2orCkuyae7SyZSz2aoLmQF2s0Pj5gITDebwPH7GFBlqOUQ1Q==", "dev": true, "engines": {"node": "*"}}, "node_modules/extract-zip/node_modules/isarray": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz", "integrity": "sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ==", "dev": true}, "node_modules/extract-zip/node_modules/minimist": {"version": "0.0.8", "resolved": "https://registry.npmjs.org/minimist/-/minimist-0.0.8.tgz", "integrity": "sha512-miQKw5Hv4NS1Psg2517mV4e4dYNaO3++hjAvLOAzKqZ61rH8NS1SK+vbfBWZ5PY/Me/bEWhUwqMghEW5Fb9T7Q==", "dev": true}, "node_modules/extract-zip/node_modules/mkdirp": {"version": "0.5.0", "resolved": "https://registry.npmjs.org/mkdirp/-/mkdirp-0.5.0.tgz", "integrity": "sha512-xjjNGy+ry1lhtIKcr2PT6ok3aszhQfgrUDp4OZLHacgRgFmF6XR9XCOJVcXlVGQonIqXcK1DvqgKKQOPWYGSfw==", "deprecated": "Legacy versions of mkdirp are no longer supported. Please update to mkdirp 1.x. (Note that the API surface has changed to use Promises in 1.x.)", "dev": true, "dependencies": {"minimist": "0.0.8"}, "bin": {"mkdirp": "bin/cmd.js"}}, "node_modules/extract-zip/node_modules/process-nextick-args": {"version": "1.0.7", "resolved": "https://registry.npmjs.org/process-nextick-args/-/process-nextick-args-1.0.7.tgz", "integrity": "sha512-yN0WQmuCX63LP/TMvAg31nvT6m4vDqJEiiv2CAZqWOGNWutc9DfDk1NPYYmKUFmaVM2UwDowH4u5AHWYP/jxKw==", "dev": true}, "node_modules/extract-zip/node_modules/readable-stream": {"version": "2.0.6", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.0.6.tgz", "integrity": "sha512-TXcFfb63BQe1+ySzsHZI/5v1aJPCShfqvWJ64ayNImXMsN1Cd0YGk/wm8KB7/OeessgPc9QvS9Zou8QTkFzsLw==", "dev": true, "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "~1.0.0", "process-nextick-args": "~1.0.6", "string_decoder": "~0.10.x", "util-deprecate": "~1.0.1"}}, "node_modules/extsprintf": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/extsprintf/-/extsprintf-1.3.0.tgz", "integrity": "sha512-11Ndz7Nv+mvAC1j0ktTa7fAb0vLyGGX+rMHNBYQviQDGU0Hw7lhctJANqbPhu9nV9/izT/IntTgZ7Im/9LJs9g==", "dev": true, "engines": ["node >=0.6.0"]}, "node_modules/faye-websocket": {"version": "0.4.4", "resolved": "https://registry.npmjs.org/faye-websocket/-/faye-websocket-0.4.4.tgz", "integrity": "sha512-78pqrJbvGZSe8i+PLsPd+aJqTyGqgyWLnMw5NOwtXCTVMzEFh1zQPwIuIL/ycTj4rkDy5zZ9B6frYPqVPJBzyQ==", "dev": true, "engines": {"node": ">=0.4.0"}}, "node_modules/fd-slicer": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/fd-slicer/-/fd-slicer-1.0.1.tgz", "integrity": "sha512-MX1ZLPIuKED51hrI4++K+1B0VX87Cs4EkybD2q12Ysuf5p4vkmHqMvQJRlDwROqFr4D2Pzyit5wGQxf30grIcw==", "dev": true, "dependencies": {"pend": "~1.2.0"}}, "node_modules/figures": {"version": "1.7.0", "resolved": "https://registry.npmjs.org/figures/-/figures-1.7.0.tgz", "integrity": "sha512-UxKlfCRuCBxSXU4C6t9scbDyWZ4VlaFFdojKtzJuSkuOBQ5CNFum+zZXFwHjo+CxBC1t6zlYPgHIgFjL8ggoEQ==", "dev": true, "dependencies": {"escape-string-regexp": "^1.0.5", "object-assign": "^4.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/findup-sync": {"version": "0.1.3", "resolved": "https://registry.npmjs.org/findup-sync/-/findup-sync-0.1.3.tgz", "integrity": "sha512-yjftfYnF4ThYEvKEV/kEFR15dmtyXTAh3vQnzpJUoc7Naj5y1P0Ck7Zs1+Vroa00E3KT3IYsk756S+8WA5dNLw==", "dev": true, "dependencies": {"glob": "~3.2.9", "lodash": "~2.4.1"}, "engines": {"node": ">= 0.6.0"}}, "node_modules/findup-sync/node_modules/glob": {"version": "3.2.11", "resolved": "https://registry.npmjs.org/glob/-/glob-3.2.11.tgz", "integrity": "sha512-hVb0zwEZwC1FXSKRPFTeOtN7AArJcJlI6ULGLtrstaswKNlrTJqAA+1lYlSUop4vjA423xlBzqfVS3iWGlqJ+g==", "deprecated": "Glob versions prior to v9 are no longer supported", "dev": true, "dependencies": {"inherits": "2", "minimatch": "0.3"}, "engines": {"node": "*"}}, "node_modules/findup-sync/node_modules/lodash": {"version": "2.4.2", "resolved": "https://registry.npmjs.org/lodash/-/lodash-2.4.2.tgz", "integrity": "sha512-Kak1hi6/hYHGVPmdyiZijoQyz5x2iGVzs6w9GYB/HiXEtylY7tIoYEROMjvM1d9nXJqPOrG2MNPMn01bJ+S0Rw==", "dev": true, "engines": ["node", "rhino"]}, "node_modules/findup-sync/node_modules/minimatch": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-0.3.0.tgz", "integrity": "sha512-WFX1jI1AaxNTZVOHLBVazwTWKaQjoykSzCBNXB72vDTCzopQGtyP91tKdFK5cv1+qMwPyiTu1HqUriqplI8pcA==", "deprecated": "Please update to minimatch 3.0.2 or higher to avoid a RegExp DoS issue", "dev": true, "dependencies": {"lru-cache": "2", "sigmund": "~1.0.0"}, "engines": {"node": "*"}}, "node_modules/finished": {"version": "1.2.2", "resolved": "https://registry.npmjs.org/finished/-/finished-1.2.2.tgz", "integrity": "sha512-HPJ8x7Gn1pmTS1zWyMoXmQ1yxHkYHRoFsBI66ONq4PS9iWBJy1iHYXOSqMWNp3ksMXfrBpenkSwBhl9WG4zr4Q==", "dev": true, "dependencies": {"ee-first": "1.0.3"}}, "node_modules/forever-agent": {"version": "0.5.2", "resolved": "https://registry.npmjs.org/forever-agent/-/forever-agent-0.5.2.tgz", "integrity": "sha512-PDG5Ef0Dob/JsZUxUltJOhm/Y9mlteAE+46y3M9RBz/Rd3QVENJ75aGRhN56yekTUboaBIkd8KVWX2NjF6+91A==", "dev": true, "engines": {"node": "*"}}, "node_modules/form-data": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/form-data/-/form-data-0.1.4.tgz", "integrity": "sha512-x8eE+nzFtAMA0YYlSxf/Qhq6vP1f8wSoZ7Aw1GuctBcmudCNuTUmmx45TfEplyb6cjsZO/jvh6+1VpZn24ez+w==", "dev": true, "optional": true, "dependencies": {"async": "~0.9.0", "combined-stream": "~0.0.4", "mime": "~1.2.11"}, "engines": {"node": ">= 0.8"}}, "node_modules/form-data/node_modules/async": {"version": "0.9.2", "resolved": "https://registry.npmjs.org/async/-/async-0.9.2.tgz", "integrity": "sha512-l6ToIJIotphWahxxHyzK9bnLR6kM4jJIIgLShZeqLY7iboHoGkdgFl7W2/Ivi4SkMJYGKqW8vSuk0uKUj6qsSw==", "dev": true, "optional": true}, "node_modules/formidable": {"version": "1.0.14", "resolved": "https://registry.npmjs.org/formidable/-/formidable-1.0.14.tgz", "integrity": "sha512-aOskFHEfYwkSKSzGui5jhQ+uyLo2NTwpzhndggz2YZHlv0HkAi+zG5ZEBCL3GTvqLyr/FzX9Mvx9DueCmu2HzQ==", "deprecated": "Please upgrade to latest, formidable@v2 or formidable@v3! Check these notes: https://bit.ly/2ZEqIau", "dev": true, "engines": {"node": ">=0.8.0"}}, "node_modules/fresh": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/fresh/-/fresh-0.2.2.tgz", "integrity": "sha512-ZGGi8GROK//ijm2gB33sUuN9TjN1tC/dvG4Bt4j6IWrVGpMmudUBCxx+Ir7qePsdREfkpQC4FL8W0jeSOsgv1w==", "dev": true}, "node_modules/fs-extra": {"version": "0.9.1", "resolved": "https://registry.npmjs.org/fs-extra/-/fs-extra-0.9.1.tgz", "integrity": "sha512-yctbTOCHGDktj3eorstAjY0Z8WtbzoHlKKiUZRbX25b34BnuIGsdI9HG2dYO72kimROUPRgJhdSxlBRhDtEZqw==", "dev": true, "dependencies": {"jsonfile": "~1.1.0", "mkdirp": "^0.5.0", "ncp": "^0.5.1", "rimraf": "^2.2.8"}}, "node_modules/gaze": {"version": "0.5.2", "resolved": "https://registry.npmjs.org/gaze/-/gaze-0.5.2.tgz", "integrity": "sha512-3IWbXGkDDHFX8zIlNdfnmhvlSMhpBO6tDr4InB8fGku6dh/gjFPGNqcdsXJajZg05x9jRzXbL6gCnCnuMap4tw==", "dev": true, "dependencies": {"globule": "~0.1.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/generate-function": {"version": "2.3.1", "resolved": "https://registry.npmjs.org/generate-function/-/generate-function-2.3.1.tgz", "integrity": "sha512-eeB5GfMNeevm/GRYq20ShmsaGcmI81kIX2K9XQx5miC8KdHaC6Jm0qQ8ZNeGOi7wYB8OsdxKs+Y2oVuTFuVwKQ==", "dev": true, "dependencies": {"is-property": "^1.0.2"}}, "node_modules/generate-object-property": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/generate-object-property/-/generate-object-property-1.2.0.tgz", "integrity": "sha512-TuOwZWgJ2VAMEGJvAyPWvpqxSANF0LDpmyHauMjFYzaACvn+QTT/AZomvPCzVBV7yDN3OmwHQ5OvHaeLKre3JQ==", "dev": true, "dependencies": {"is-property": "^1.0.0"}}, "node_modules/getobject": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/getobject/-/getobject-0.1.0.tgz", "integrity": "sha512-hIGEBfnHcZpWkXPsAVeVmpYDvfy/matVl03yOY91FPmnpCC12Lm5izNxCjO3lHAeO6uaTwMxu7g450Siknlhig==", "dev": true, "engines": {"node": ">= 0.8.0"}}, "node_modules/getpass": {"version": "0.1.7", "resolved": "https://registry.npmjs.org/getpass/-/getpass-0.1.7.tgz", "integrity": "sha512-0fzj9JxOLfJ+XGLhR8ze3unN0KZCgZwiSSDz168VERjK8Wl8kVSdcu2kspd4s4wtAa1y/qrVRiAA0WclVsu0ng==", "dev": true, "dependencies": {"assert-plus": "^1.0.0"}}, "node_modules/getpass/node_modules/assert-plus": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/assert-plus/-/assert-plus-1.0.0.tgz", "integrity": "sha512-NfJ4UzBCcQGLDlQq7nHxH+tv3kyZ0hHQqF5BO6J7tNJeP5do1llPr8dZ8zHonfhAu0PHAdMkSo+8o0wxg9lZWw==", "dev": true, "engines": {"node": ">=0.8"}}, "node_modules/glob": {"version": "4.0.6", "resolved": "https://registry.npmjs.org/glob/-/glob-4.0.6.tgz", "integrity": "sha512-D0H1thJnOVgI0zRV3H/Vmb9HWmDgGTTR7PeT8Lk0ri2kMmfK3oKQBolfqJuRpBVpTx5Q5PKGl9hdQEQNTXJI7Q==", "deprecated": "Glob versions prior to v9 are no longer supported", "dev": true, "dependencies": {"graceful-fs": "^3.0.2", "inherits": "2", "minimatch": "^1.0.0", "once": "^1.3.0"}, "engines": {"node": "*"}}, "node_modules/globule": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/globule/-/globule-0.1.0.tgz", "integrity": "sha512-3eIcA2OjPCm4VvwIwZPzIxCVssA8HSpM2C6c6kK5ufJH4FGwWoyqL3In19uuX4oe+TwH3w2P1nQDmW56iehO4A==", "dev": true, "dependencies": {"glob": "~3.1.21", "lodash": "~1.0.1", "minimatch": "~0.2.11"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/globule/node_modules/glob": {"version": "3.1.21", "resolved": "https://registry.npmjs.org/glob/-/glob-3.1.21.tgz", "integrity": "sha512-ANhy2V2+tFpRajE3wN4DhkNQ08KDr0Ir1qL12/cUe5+a7STEK8jkW4onUYuY8/06qAFuT5je7mjAqzx0eKI2tQ==", "deprecated": "Glob versions prior to v9 are no longer supported", "dev": true, "dependencies": {"graceful-fs": "~1.2.0", "inherits": "1", "minimatch": "~0.2.11"}, "engines": {"node": "*"}}, "node_modules/globule/node_modules/graceful-fs": {"version": "1.2.3", "resolved": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-1.2.3.tgz", "integrity": "sha512-iiTUZ5vZ+2ZV+h71XAgwCSu6+NAizhFU3Yw8aC/hH5SQ3SnISqEqAek40imAFGtDcwJKNhXvSY+hzIolnLwcdQ==", "deprecated": "please upgrade to graceful-fs 4 for compatibility with current and future versions of Node.js", "dev": true, "engines": {"node": ">=0.4.0"}}, "node_modules/globule/node_modules/inherits": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/inherits/-/inherits-1.0.2.tgz", "integrity": "sha512-Al67oatbRSo3RV5hRqIoln6Y5yMVbJSIn4jEJNL7VCImzq/kLr7vvb6sFRJXqr8rpHc/2kJOM+y0sPKN47VdzA==", "dev": true}, "node_modules/globule/node_modules/lodash": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/lodash/-/lodash-1.0.2.tgz", "integrity": "sha512-0VSEDVec/Me2eATuoiQd8IjyBMMX0fahob8YJ96V1go2RjvCk1m1GxmtfXn8RNSaLaTtop7fsuhhu9oLk3hUgA==", "dev": true, "engines": ["node", "rhino"]}, "node_modules/globule/node_modules/minimatch": {"version": "0.2.14", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-0.2.14.tgz", "integrity": "sha512-zZ+Jy8lVWlvqqeM8iZB7w7KmQkoJn8djM585z88rywrEbzoqawVa9FR5p2hwD+y74nfuKOjmNvi9gtWJNLqHvA==", "deprecated": "Please update to minimatch 3.0.2 or higher to avoid a RegExp DoS issue", "dev": true, "dependencies": {"lru-cache": "2", "sigmund": "~1.0.0"}, "engines": {"node": "*"}}, "node_modules/gonzales-pe": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/gonzales-pe/-/gonzales-pe-2.0.2.tgz", "integrity": "sha512-RpskFL5GRsQa+ZfB9cAyxiv5ZNg5/eY77g+8qfgjxdcqTe260Zmj1jGmVU7jJbJoBwMrH1wVl5o2z4a84dCVaQ==", "dev": true, "engines": {"node": ">=0.6.0"}}, "node_modules/graceful-fs": {"version": "3.0.12", "resolved": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-3.0.12.tgz", "integrity": "sha512-J55gaCS4iTTJfTXIxSVw3EMQckcqkpdRv3IR7gu6sq0+tbC363Zx6KH/SEwXASK9JRbhyZmVjJEVJIOxYsB3Qg==", "dev": true, "dependencies": {"natives": "^1.1.3"}, "engines": {"node": ">=0.4.0"}}, "node_modules/grunt": {"version": "0.4.5", "resolved": "https://registry.npmjs.org/grunt/-/grunt-0.4.5.tgz", "integrity": "sha512-1iq3ylLjzXqz/KSq1OAE2qhnpcbkF2WyhsQcavZt+YmgvHu0EbPMEhGhy2gr0FP67isHpRdfwjB5WVeXXcJemQ==", "dev": true, "dependencies": {"async": "~0.1.22", "coffee-script": "~1.3.3", "colors": "~0.6.2", "dateformat": "1.0.2-1.2.3", "eventemitter2": "~0.4.13", "exit": "~0.1.1", "findup-sync": "~0.1.2", "getobject": "~0.1.0", "glob": "~3.1.21", "grunt-legacy-log": "~0.1.0", "grunt-legacy-util": "~0.2.0", "hooker": "~0.2.3", "iconv-lite": "~0.2.11", "js-yaml": "~2.0.5", "lodash": "~0.9.2", "minimatch": "~0.2.12", "nopt": "~1.0.10", "rimraf": "~2.2.8", "underscore.string": "~2.2.1", "which": "~1.0.5"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/grunt-autoprefixer": {"version": "0.7.6", "resolved": "https://registry.npmjs.org/grunt-autoprefixer/-/grunt-autoprefixer-0.7.6.tgz", "integrity": "sha512-4XsDXVYQdHYP2tj+dKpAQo0jS9QXyqHiGwrrGZOZRaPg9a46w6Ul6Sm9UkN+xWekAa1HyiKEKpue8vlbtrEHUA==", "dev": true, "dependencies": {"autoprefixer": "~1.3.1", "chalk": "~0.4.0", "diff": "~1.0.8"}, "engines": {"node": ">= 0.10.0"}, "peerDependencies": {"grunt": "~0.4.2"}}, "node_modules/grunt-banner": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/grunt-banner/-/grunt-banner-0.2.3.tgz", "integrity": "sha512-WwHgvZMR9rZIbrNRQfc5iIN0SoJKNPgSeBuMMHMeLZ/EDw6sArmSqMqRoUYxt8ItYLPyKYjbdPtCFJH+2wAo+g==", "dev": true, "engines": {"node": ">= 0.8.0"}, "peerDependencies": {"grunt": "~0.4.1"}}, "node_modules/grunt-contrib-clean": {"version": "0.5.0", "resolved": "https://registry.npmjs.org/grunt-contrib-clean/-/grunt-contrib-clean-0.5.0.tgz", "integrity": "sha512-82I+O38BHP4rqxceareiDKnLnrSyGnyn6N9E6sUpBgCtxXgxMbJEBooSC7KtXhLO7im1QhoPp9FdSvTZ+k1bIw==", "dev": true, "dependencies": {"rimraf": "~2.2.1"}, "engines": {"node": ">= 0.8.0"}, "peerDependencies": {"grunt": "~0.4.0"}}, "node_modules/grunt-contrib-concat": {"version": "0.4.0", "resolved": "https://registry.npmjs.org/grunt-contrib-concat/-/grunt-contrib-concat-0.4.0.tgz", "integrity": "sha512-H6oK<PERSON>4bChJaEpXSd5DRle0iof32RhkRMMsHHUNhgaocjd2AkrayBMeB728jroqEyoLZDgjHZQf9l/B6Qzl7hfQ==", "dev": true, "dependencies": {"chalk": "~0.4.0"}, "engines": {"node": ">= 0.8.0"}, "peerDependencies": {"grunt": "~0.4.0"}}, "node_modules/grunt-contrib-connect": {"version": "0.8.0", "resolved": "https://registry.npmjs.org/grunt-contrib-connect/-/grunt-contrib-connect-0.8.0.tgz", "integrity": "sha512-PUsVXW5kG3etTyF45/JWJByozFW7kzDbkS/aOFxF7/HYghO2Sva+jz0K7rR8FUGFUgM8FYjfO3wKGU+4/oH30Q==", "dev": true, "dependencies": {"async": "~0.9.0", "connect": "~2.19.5", "connect-livereload": "~0.4.0", "open": "0.0.5", "portscanner": "~0.2.3"}, "engines": {"node": ">= 0.8.0"}, "peerDependencies": {"grunt": "~0.4.0"}}, "node_modules/grunt-contrib-connect/node_modules/async": {"version": "0.9.2", "resolved": "https://registry.npmjs.org/async/-/async-0.9.2.tgz", "integrity": "sha512-l6ToIJIotphWahxxHyzK9bnLR6kM4jJIIgLShZeqLY7iboHoGkdgFl7W2/Ivi4SkMJYGKqW8vSuk0uKUj6qsSw==", "dev": true}, "node_modules/grunt-contrib-copy": {"version": "0.5.0", "resolved": "https://registry.npmjs.org/grunt-contrib-copy/-/grunt-contrib-copy-0.5.0.tgz", "integrity": "sha512-qJkJqvttTuVV7hXaQ91ctB1Anha0z6pyZuBlz+Trw8O5tFJ5hpB5f3BNxMfSgO7ciSgw36FgAovLg992x3dqKw==", "dev": true, "engines": {"node": ">= 0.8.0"}, "peerDependencies": {"grunt": "~0.4.0"}}, "node_modules/grunt-contrib-csslint": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/grunt-contrib-csslint/-/grunt-contrib-csslint-0.2.0.tgz", "integrity": "sha512-6QrhYtoC3HKiRIfDkfFd2d7+RexDXYaIly3F9znNL4T9SichMFH88CUPbFZb6PcMppYTnoXbCHlenEjVTo3C6A==", "dev": true, "dependencies": {"csslint": "~0.10.0"}, "engines": {"node": ">= 0.8.0"}, "peerDependencies": {"grunt": "~0.4.0"}}, "node_modules/grunt-contrib-cssmin": {"version": "0.10.0", "resolved": "https://registry.npmjs.org/grunt-contrib-cssmin/-/grunt-contrib-cssmin-0.10.0.tgz", "integrity": "sha512-mor0RZXcdGkRZoqj+VweMwW0Iq+67EiGYD9OmLtNtJjOjZmUMoOOmLOXHLxpLW84ZgEEySAgik05HiiE2v9cvA==", "dev": true, "dependencies": {"chalk": "~0.4.0", "clean-css": "~2.2.0", "maxmin": "~0.2.0"}, "engines": {"node": ">= 0.8.0"}, "peerDependencies": {"grunt": "~0.4.1"}}, "node_modules/grunt-contrib-jade": {"version": "0.12.0", "resolved": "https://registry.npmjs.org/grunt-contrib-jade/-/grunt-contrib-jade-0.12.0.tgz", "integrity": "sha512-zuPrZZ7+3eX4Im5S8c2hayCwP72SDjgmd1t4GBh/jkm893VPam2Q1edPUzkJu4eXEDSGsP0eav4GvNO7ZBzebg==", "dev": true, "dependencies": {"chalk": "~0.4.0", "grunt-lib-contrib": "~0.6.1", "jade": "~1.3.0"}, "engines": {"node": ">= 0.8.0"}, "peerDependencies": {"grunt": "~0.4.1"}}, "node_modules/grunt-contrib-jshint": {"version": "0.10.0", "resolved": "https://registry.npmjs.org/grunt-contrib-jshint/-/grunt-contrib-jshint-0.10.0.tgz", "integrity": "sha512-NiWkzF8RM9IxFiv7YLDKouNOdNwF3Ot0GNiXFXjkc4RjuE82pKC3aj4+gFGX8hIe+d+BlWAMKPhT2QeNDAu0ew==", "dev": true, "dependencies": {"hooker": "~0.2.3", "jshint": "~2.5.0"}, "engines": {"node": ">= 0.8.0"}, "peerDependencies": {"grunt": "~0.4.0"}}, "node_modules/grunt-contrib-less": {"version": "0.11.4", "resolved": "https://registry.npmjs.org/grunt-contrib-less/-/grunt-contrib-less-0.11.4.tgz", "integrity": "sha512-IOelFvXMPKOzGxdfLD2IK8PCkzEd+LPRpE/y4xPPKAkHOEiE0jkDbbkpzVecQvDJ4os4yPwb0xtgQ90fWmBbag==", "dev": true, "dependencies": {"async": "^0.2.10", "chalk": "^0.5.1", "less": "^1.7.2", "lodash": "^2.4.1", "maxmin": "^0.1.0"}, "engines": {"node": ">=0.10.0"}, "peerDependencies": {"grunt": "~0.4.0"}}, "node_modules/grunt-contrib-less/node_modules/ansi-styles": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-1.1.0.tgz", "integrity": "sha512-f2PKUkN5QngiSemowa6Mrk9MPCdtFiOSmibjZ+j1qhLGHHYsqZwmBMRF3IRMVXo8sybDqx2fJl2d/8OphBoWkA==", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/grunt-contrib-less/node_modules/async": {"version": "0.2.10", "resolved": "https://registry.npmjs.org/async/-/async-0.2.10.tgz", "integrity": "sha512-eAkdoKxU6/LkKDBzLpT+t6Ff5EtfSF4wx1WfJiPEEV7WNLnDaRXk0oVysiEPm262roaachGexwUv94WhSgN5TQ==", "dev": true}, "node_modules/grunt-contrib-less/node_modules/chalk": {"version": "0.5.1", "resolved": "https://registry.npmjs.org/chalk/-/chalk-0.5.1.tgz", "integrity": "sha512-bIKA54hP8iZhyDT81TOsJiQvR1gW+ZYSXFaZUAvoD4wCHdbHY2actmpTE4x344ZlFqHbvoxKOaESULTZN2gstg==", "dev": true, "dependencies": {"ansi-styles": "^1.1.0", "escape-string-regexp": "^1.0.0", "has-ansi": "^0.1.0", "strip-ansi": "^0.3.0", "supports-color": "^0.2.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/grunt-contrib-less/node_modules/gzip-size": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/gzip-size/-/gzip-size-0.1.1.tgz", "integrity": "sha512-G7KBsKYrNbs0BuoiPkRvbbI+mpa9wfjYTcdAyh9/eo9rBy05F/csSMmbNTkPgKY+nWgUszPV6kQ8lm0E9vBTnQ==", "dev": true, "dependencies": {"concat-stream": "^1.4.1", "zlib-browserify": "^0.0.3"}, "bin": {"gzip-size": "cli.js"}, "engines": {"node": ">=0.10.0"}}, "node_modules/grunt-contrib-less/node_modules/lodash": {"version": "2.4.2", "resolved": "https://registry.npmjs.org/lodash/-/lodash-2.4.2.tgz", "integrity": "sha512-Kak1hi6/hYHGVPmdyiZijoQyz5x2iGVzs6w9GYB/HiXEtylY7tIoYEROMjvM1d9nXJqPOrG2MNPMn01bJ+S0Rw==", "dev": true, "engines": ["node", "rhino"]}, "node_modules/grunt-contrib-less/node_modules/maxmin": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/maxmin/-/maxmin-0.1.0.tgz", "integrity": "sha512-grP8Hy0DIXklhjV9qWtQS2i5DIUYaErKIjxIa5N8pq/fka7khzyYuyimAYszFlWmMw6ZTsR+uwS6fjhGy+5vyg==", "dev": true, "dependencies": {"chalk": "^0.4.0", "gzip-size": "^0.1.0", "pretty-bytes": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/grunt-contrib-less/node_modules/maxmin/node_modules/ansi-styles": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-1.0.0.tgz", "integrity": "sha512-3iF4FIKdxaVYT3JqQuY3Wat/T2t7TRbbQ94Fu50ZUCbLy4TFbTzr90NOHQodQkNqmeEGCw8WbeP78WNi6SKYUA==", "dev": true, "engines": {"node": ">=0.8.0"}}, "node_modules/grunt-contrib-less/node_modules/maxmin/node_modules/chalk": {"version": "0.4.0", "resolved": "https://registry.npmjs.org/chalk/-/chalk-0.4.0.tgz", "integrity": "sha512-sQfYDlfv2DGVtjdoQqxS0cEZDroyG8h6TamA6rvxwlrU5BaSLDx9xhatBYl2pxZ7gmpNaPFVwBtdGdu5rQ+tYQ==", "dev": true, "dependencies": {"ansi-styles": "~1.0.0", "has-color": "~0.1.0", "strip-ansi": "~0.1.0"}, "engines": {"node": ">=0.8.0"}}, "node_modules/grunt-contrib-less/node_modules/maxmin/node_modules/strip-ansi": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-0.1.1.tgz", "integrity": "sha512-behete+3uqxecWlDAm5lmskaSaISA+ThQ4oNNBDTBJt0x2ppR6IPqfZNuj6BLaLJ/Sji4TPZlcRyOis8wXQTLg==", "dev": true, "bin": {"strip-ansi": "cli.js"}, "engines": {"node": ">=0.8.0"}}, "node_modules/grunt-contrib-less/node_modules/strip-ansi": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-0.3.0.tgz", "integrity": "sha512-DerhZL7j6i6/nEnVG0qViKXI0OKouvvpsAiaj7c+LfqZZZxdwZtv8+UiA/w4VUJpT8UzX0pR1dcHOii1GbmruQ==", "dev": true, "dependencies": {"ansi-regex": "^0.2.1"}, "bin": {"strip-ansi": "cli.js"}, "engines": {"node": ">=0.10.0"}}, "node_modules/grunt-contrib-less/node_modules/zlib-browserify": {"version": "0.0.3", "resolved": "https://registry.npmjs.org/zlib-browserify/-/zlib-browserify-0.0.3.tgz", "integrity": "sha512-KW42YGoQKq7+oU46deeWMUsrPyBruEWV1DoObBTMfEC2LnTqQIrwKVKrPoz2mn5DXESW4Ca/lIP2MqGHrt/WFA==", "dev": true, "dependencies": {"tape": "~0.2.2"}}, "node_modules/grunt-contrib-qunit": {"version": "0.5.2", "resolved": "https://registry.npmjs.org/grunt-contrib-qunit/-/grunt-contrib-qunit-0.5.2.tgz", "integrity": "sha512-7gWHpmwINAStSQQE2QO1xPhHUotd+hV4Jv6BpGzu13FNXmk+lOd19nqfX3bB8YRKw5F5Eh4FAly5f6ohTXWLew==", "dev": true, "dependencies": {"grunt-lib-phantomjs": "~0.6.0"}, "engines": {"node": ">= 0.8.0"}, "peerDependencies": {"grunt": "~0.4.0"}}, "node_modules/grunt-contrib-uglify": {"version": "0.5.1", "resolved": "https://registry.npmjs.org/grunt-contrib-uglify/-/grunt-contrib-uglify-0.5.1.tgz", "integrity": "sha512-3BBGfjVFUax3DeCuS8TerPZmEJ79GaIHw1m+HCFfSufpFnI37ySZb13iUewl3opQYPKCKXZrHa/xw1qg+J5qKg==", "dev": true, "dependencies": {"chalk": "^0.5.1", "lodash": "^2.4.1", "maxmin": "^0.2.0", "uglify-js": "^2.4.0"}, "engines": {"node": ">=0.10.0"}, "peerDependencies": {"grunt": "~0.4.0"}}, "node_modules/grunt-contrib-uglify/node_modules/ansi-styles": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-1.1.0.tgz", "integrity": "sha512-f2PKUkN5QngiSemowa6Mrk9MPCdtFiOSmibjZ+j1qhLGHHYsqZwmBMRF3IRMVXo8sybDqx2fJl2d/8OphBoWkA==", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/grunt-contrib-uglify/node_modules/chalk": {"version": "0.5.1", "resolved": "https://registry.npmjs.org/chalk/-/chalk-0.5.1.tgz", "integrity": "sha512-bIKA54hP8iZhyDT81TOsJiQvR1gW+ZYSXFaZUAvoD4wCHdbHY2actmpTE4x344ZlFqHbvoxKOaESULTZN2gstg==", "dev": true, "dependencies": {"ansi-styles": "^1.1.0", "escape-string-regexp": "^1.0.0", "has-ansi": "^0.1.0", "strip-ansi": "^0.3.0", "supports-color": "^0.2.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/grunt-contrib-uglify/node_modules/lodash": {"version": "2.4.2", "resolved": "https://registry.npmjs.org/lodash/-/lodash-2.4.2.tgz", "integrity": "sha512-Kak1hi6/hYHGVPmdyiZijoQyz5x2iGVzs6w9GYB/HiXEtylY7tIoYEROMjvM1d9nXJqPOrG2MNPMn01bJ+S0Rw==", "dev": true, "engines": ["node", "rhino"]}, "node_modules/grunt-contrib-uglify/node_modules/strip-ansi": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-0.3.0.tgz", "integrity": "sha512-DerhZL7j6i6/nEnVG0qViKXI0OKouvvpsAiaj7c+LfqZZZxdwZtv8+UiA/w4VUJpT8UzX0pR1dcHOii1GbmruQ==", "dev": true, "dependencies": {"ansi-regex": "^0.2.1"}, "bin": {"strip-ansi": "cli.js"}, "engines": {"node": ">=0.10.0"}}, "node_modules/grunt-contrib-watch": {"version": "0.6.1", "resolved": "https://registry.npmjs.org/grunt-contrib-watch/-/grunt-contrib-watch-0.6.1.tgz", "integrity": "sha512-Ea4f3anehA6G+S88vyTwewA/Mf6SbadTIxSgQClKbZYnHOcJ0PrGc2p+uzR6hS9gp7DG6ObvaxkmE8HKnLOsfA==", "dev": true, "dependencies": {"async": "~0.2.9", "gaze": "~0.5.1", "lodash": "~2.4.1", "tiny-lr-fork": "0.0.5"}, "engines": {"node": ">= 0.8.0"}, "peerDependencies": {"grunt": "~0.4.0"}}, "node_modules/grunt-contrib-watch/node_modules/async": {"version": "0.2.10", "resolved": "https://registry.npmjs.org/async/-/async-0.2.10.tgz", "integrity": "sha512-eAkdoKxU6/LkKDBzLpT+t6Ff5EtfSF4wx1WfJiPEEV7WNLnDaRXk0oVysiEPm262roaachGexwUv94WhSgN5TQ==", "dev": true}, "node_modules/grunt-contrib-watch/node_modules/lodash": {"version": "2.4.2", "resolved": "https://registry.npmjs.org/lodash/-/lodash-2.4.2.tgz", "integrity": "sha512-Kak1hi6/hYHGVPmdyiZijoQyz5x2iGVzs6w9GYB/HiXEtylY7tIoYEROMjvM1d9nXJqPOrG2MNPMn01bJ+S0Rw==", "dev": true, "engines": ["node", "rhino"]}, "node_modules/grunt-csscomb": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/grunt-csscomb/-/grunt-csscomb-2.0.1.tgz", "integrity": "sha512-+TjmBBJ3beCxdpgnRPPokmkHGozUjUaajwA2b0PQFzzrVmzaCCpnvO51hY1ydjyIvowyoRSRdjOIrOYmMnGDoA==", "dev": true, "dependencies": {"csscomb": "~2.0.0"}, "engines": {"node": ">= 0.10.0"}, "peerDependencies": {"grunt": "~0.4.2"}}, "node_modules/grunt-exec": {"version": "0.4.7", "resolved": "https://registry.npmjs.org/grunt-exec/-/grunt-exec-0.4.7.tgz", "integrity": "sha512-AJw+ccLkJnycdyiTXFpt1ELpRbiMDn0uj3b04W0kmmjEZCz3j1LSO2bH9GxgQniUZc2mQFMEew9Bw9tWCu+1zw==", "dev": true, "engines": {"node": ">=0.8.0"}, "peerDependencies": {"grunt": ">=0.4"}}, "node_modules/grunt-html-validation": {"version": "0.1.18", "resolved": "https://registry.npmjs.org/grunt-html-validation/-/grunt-html-validation-0.1.18.tgz", "integrity": "sha512-Mbq47nI36651jYvg4+xgq64z/H5Z7OXz2s79IHOwuTI710EJLIip5LruzeqsNgtoKBj3e9oMi1Sx+SE4croZbw==", "dev": true, "dependencies": {"colors": "~0.6.0", "request": "~2.34.0", "w3cjs": "~0.1.25"}, "engines": {"node": ">= 0.8.0"}, "peerDependencies": {"grunt": "~0.4.1"}}, "node_modules/grunt-jekyll": {"version": "0.4.7", "resolved": "https://registry.npmjs.org/grunt-jekyll/-/grunt-jekyll-0.4.7.tgz", "integrity": "sha512-sRZUN6Nu+enDzTGPSMuo7UmzsJ4Z7b3Fkt0UYEeDHKlyn6xMnBmHNggLdhTuqpCdt7tV6JFk0ISWj0iTeBFMLA==", "dev": true, "dependencies": {"tmp": "^0.0.33"}, "bin": {"grunt-jekyll": "bin/grunt-jekyll"}, "engines": {"node": ">= 0.10.5"}, "peerDependencies": {"grunt": ">=0.4.1"}}, "node_modules/grunt-jscs-checker": {"version": "0.6.2", "resolved": "https://registry.npmjs.org/grunt-jscs-checker/-/grunt-jscs-checker-0.6.2.tgz", "integrity": "sha512-hBaK2S0AraAc95GY42dFJ8QKIR9P3JovruUuaz90WPzgwxRe7BKdZFeCpEJm0HX27hhJuhGXZlhzcqQl6JV3dw==", "deprecated": "Package was renamed to 'grunt-jscs'", "dev": true, "dependencies": {"hooker": "~0.2.3", "jscs": "~1.5.9", "lodash": "~2.4.1", "vow": "~0.4.1"}, "engines": {"node": ">= 0.10.0"}, "peerDependencies": {"grunt": "~0.4.2"}}, "node_modules/grunt-jscs-checker/node_modules/lodash": {"version": "2.4.2", "resolved": "https://registry.npmjs.org/lodash/-/lodash-2.4.2.tgz", "integrity": "sha512-Kak1hi6/hYHGVPmdyiZijoQyz5x2iGVzs6w9GYB/HiXEtylY7tIoYEROMjvM1d9nXJqPOrG2MNPMn01bJ+S0Rw==", "dev": true, "engines": ["node", "rhino"]}, "node_modules/grunt-legacy-log": {"version": "0.1.3", "resolved": "https://registry.npmjs.org/grunt-legacy-log/-/grunt-legacy-log-0.1.3.tgz", "integrity": "sha512-qYs/uM0ImdzwIXLhS4O5WLV5soAM+PEqqHI/hzSxlo450ERSccEhnXqoeDA9ZozOdaWuYnzTOTwRcVRogleMxg==", "dev": true, "dependencies": {"colors": "~0.6.2", "grunt-legacy-log-utils": "~0.1.1", "hooker": "~0.2.3", "lodash": "~2.4.1", "underscore.string": "~2.3.3"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/grunt-legacy-log-utils": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/grunt-legacy-log-utils/-/grunt-legacy-log-utils-0.1.1.tgz", "integrity": "sha512-D0vbUX00TFYCKNZtcZzemMpwT8TR/FdRs1pmfiBw6qnUw80PfsjV+lhIozY/3eJ3PSG2zj89wd2mH/7f4tNAlw==", "dev": true, "dependencies": {"colors": "~0.6.2", "lodash": "~2.4.1", "underscore.string": "~2.3.3"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/grunt-legacy-log-utils/node_modules/lodash": {"version": "2.4.2", "resolved": "https://registry.npmjs.org/lodash/-/lodash-2.4.2.tgz", "integrity": "sha512-Kak1hi6/hYHGVPmdyiZijoQyz5x2iGVzs6w9GYB/HiXEtylY7tIoYEROMjvM1d9nXJqPOrG2MNPMn01bJ+S0Rw==", "dev": true, "engines": ["node", "rhino"]}, "node_modules/grunt-legacy-log-utils/node_modules/underscore.string": {"version": "2.3.3", "resolved": "https://registry.npmjs.org/underscore.string/-/underscore.string-2.3.3.tgz", "integrity": "sha512-hbD5MibthuDAu4yA5wxes5bzFgqd3PpBJuClbRxaNddxfdsz+qf+1kHwrGQFrmchmDHb9iNU+6EHDn8uj0xDJg==", "dev": true, "engines": {"node": "*"}}, "node_modules/grunt-legacy-log/node_modules/lodash": {"version": "2.4.2", "resolved": "https://registry.npmjs.org/lodash/-/lodash-2.4.2.tgz", "integrity": "sha512-Kak1hi6/hYHGVPmdyiZijoQyz5x2iGVzs6w9GYB/HiXEtylY7tIoYEROMjvM1d9nXJqPOrG2MNPMn01bJ+S0Rw==", "dev": true, "engines": ["node", "rhino"]}, "node_modules/grunt-legacy-log/node_modules/underscore.string": {"version": "2.3.3", "resolved": "https://registry.npmjs.org/underscore.string/-/underscore.string-2.3.3.tgz", "integrity": "sha512-hbD5MibthuDAu4yA5wxes5bzFgqd3PpBJuClbRxaNddxfdsz+qf+1kHwrGQFrmchmDHb9iNU+6EHDn8uj0xDJg==", "dev": true, "engines": {"node": "*"}}, "node_modules/grunt-legacy-util": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/grunt-legacy-util/-/grunt-legacy-util-0.2.0.tgz", "integrity": "sha512-cXPbfF8aM+pvveQeN1K872D5fRm30xfJWZiS63Y8W8oyIPLClCsmI8bW96Txqzac9cyL4lRqEBhbhJ3n5EzUUQ==", "dev": true, "dependencies": {"async": "~0.1.22", "exit": "~0.1.1", "getobject": "~0.1.0", "hooker": "~0.2.3", "lodash": "~0.9.2", "underscore.string": "~2.2.1", "which": "~1.0.5"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/grunt-lib-contrib": {"version": "0.6.1", "resolved": "https://registry.npmjs.org/grunt-lib-contrib/-/grunt-lib-contrib-0.6.1.tgz", "integrity": "sha512-HdCtJuMmmkSAVrAfsG7lZWE0YabrsPWwzcCCUgWQOAaQsQSUNhw/IwD2YjCSLh5y9NXSPzHTYFLL4ro7QbAJMA==", "dev": true, "dependencies": {"zlib-browserify": "0.0.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/grunt-lib-phantomjs": {"version": "0.6.0", "resolved": "https://registry.npmjs.org/grunt-lib-phantomjs/-/grunt-lib-phantomjs-0.6.0.tgz", "integrity": "sha512-+ZHPGJ84IkBOtRLcz4yG/JXZBZVO/zrANcbMQvqBb1RcC7ozjcc+XjJMPoiRUHh6ue+Q5EfwAE2IFrNwWB4vhg==", "dev": true, "dependencies": {"eventemitter2": "~0.4.9", "phantomjs": "~1.9.0-1", "semver": "~1.0.14", "temporary": "~0.0.4"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/grunt-saucelabs": {"version": "8.1.1", "resolved": "https://registry.npmjs.org/grunt-saucelabs/-/grunt-saucelabs-8.1.1.tgz", "integrity": "sha512-1tUW6Z/zeB4ba7EYZ16qnIk2ybLddgGRwh4bFFLkuwZdRfAyy115B2GiW8OqKLCk0EbVR27y/OzMr6Te2KK5CA==", "dev": true, "dependencies": {"colors": "~0.6.2", "lodash": "~2.4.1", "q": "~1.0.0", "request": "~2.35.0", "sauce-tunnel": "~2.0.6", "saucelabs": "~0.1.1"}, "engines": {"node": ">=0.6", "npm": ">=1.2.12"}, "peerDependencies": {"grunt": "~0.4.1"}}, "node_modules/grunt-saucelabs/node_modules/lodash": {"version": "2.4.2", "resolved": "https://registry.npmjs.org/lodash/-/lodash-2.4.2.tgz", "integrity": "sha512-Kak1hi6/hYHGVPmdyiZijoQyz5x2iGVzs6w9GYB/HiXEtylY7tIoYEROMjvM1d9nXJqPOrG2MNPMn01bJ+S0Rw==", "dev": true, "engines": ["node", "rhino"]}, "node_modules/grunt-saucelabs/node_modules/request": {"version": "2.35.0", "resolved": "https://registry.npmjs.org/request/-/request-2.35.0.tgz", "integrity": "sha512-l6TZWFCEwywj3eLWikFkIcrTWJBkX5db6Yz/NGC1RzwAujF86jzhvxv5EQKREf+jSduKNIUhh4+2QH3o3pUULQ==", "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "dev": true, "engines": ["node >= 0.8.0"], "dependencies": {"forever-agent": "~0.5.0", "json-stringify-safe": "~5.0.0", "lodash.merge": "~2.4.1", "mime": "~1.2.9", "node-uuid": "~1.4.0", "qs": "~0.6.0"}, "optionalDependencies": {"aws-sign2": "~0.5.0", "form-data": "~0.1.0", "hawk": "~1.0.0", "http-signature": "~0.10.0", "oauth-sign": "~0.3.0", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0"}}, "node_modules/grunt-saucelabs/node_modules/tunnel-agent": {"version": "0.4.3", "resolved": "https://registry.npmjs.org/tunnel-agent/-/tunnel-agent-0.4.3.tgz", "integrity": "sha512-e0IoVDWx8SDHc/hwFTqJDQ7CCDTEeGhmcT9jkWJjoGQSpgBz20nAMr80E3Tpk7PatJ1b37DQDgJR3CNSzcMOZQ==", "dev": true, "optional": true, "engines": {"node": "*"}}, "node_modules/grunt-sed": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/grunt-sed/-/grunt-sed-0.1.1.tgz", "integrity": "sha512-70jT0SJrvYWtgZ6pzG/rOFEZtV8bmC4YhkutSkQ38osv8lumb+UkFIrdN49ArlJSmfUo+SqdIKQsMtzpy8fOaw==", "dev": true, "dependencies": {"replace": "~0.2.4"}, "engines": {"node": ">=0.8.0"}, "peerDependencies": {"grunt": "~0.4"}}, "node_modules/grunt/node_modules/glob": {"version": "3.1.21", "resolved": "https://registry.npmjs.org/glob/-/glob-3.1.21.tgz", "integrity": "sha512-ANhy2V2+tFpRajE3wN4DhkNQ08KDr0Ir1qL12/cUe5+a7STEK8jkW4onUYuY8/06qAFuT5je7mjAqzx0eKI2tQ==", "deprecated": "Glob versions prior to v9 are no longer supported", "dev": true, "dependencies": {"graceful-fs": "~1.2.0", "inherits": "1", "minimatch": "~0.2.11"}, "engines": {"node": "*"}}, "node_modules/grunt/node_modules/graceful-fs": {"version": "1.2.3", "resolved": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-1.2.3.tgz", "integrity": "sha512-iiTUZ5vZ+2ZV+h71XAgwCSu6+NAizhFU3Yw8aC/hH5SQ3SnISqEqAek40imAFGtDcwJKNhXvSY+hzIolnLwcdQ==", "deprecated": "please upgrade to graceful-fs 4 for compatibility with current and future versions of Node.js", "dev": true, "engines": {"node": ">=0.4.0"}}, "node_modules/grunt/node_modules/inherits": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/inherits/-/inherits-1.0.2.tgz", "integrity": "sha512-Al67oatbRSo3RV5hRqIoln6Y5yMVbJSIn4jEJNL7VCImzq/kLr7vvb6sFRJXqr8rpHc/2kJOM+y0sPKN47VdzA==", "dev": true}, "node_modules/grunt/node_modules/minimatch": {"version": "0.2.14", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-0.2.14.tgz", "integrity": "sha512-zZ+Jy8lVWlvqqeM8iZB7w7KmQkoJn8djM585z88rywrEbzoqawVa9FR5p2hwD+y74nfuKOjmNvi9gtWJNLqHvA==", "deprecated": "Please update to minimatch 3.0.2 or higher to avoid a RegExp DoS issue", "dev": true, "dependencies": {"lru-cache": "2", "sigmund": "~1.0.0"}, "engines": {"node": "*"}}, "node_modules/gzip-size": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/gzip-size/-/gzip-size-0.2.0.tgz", "integrity": "sha512-0b7+rnImJJ2ShduJuITFweChFcOOHmxLBTfNczxTFsidRzfG/w5GJl44ps1z84z3oR3gYvjn0Um6qOL1jqPyRQ==", "dev": true, "dependencies": {"browserify-zlib": "^0.1.4", "concat-stream": "^1.4.1"}, "bin": {"gzip-size": "cli.js"}, "engines": {"node": ">=0.10.0"}}, "node_modules/har-validator": {"version": "2.0.6", "resolved": "https://registry.npmjs.org/har-validator/-/har-validator-2.0.6.tgz", "integrity": "sha512-P6tFV+wCcUL3nbyTDAvveDySfbhy0XkDtAIfZP6HITjM2WUsiPna/Eg1Yy93SFXvahqoX+kt0n+6xlXKDXYowA==", "deprecated": "this library is no longer supported", "dev": true, "dependencies": {"chalk": "^1.1.1", "commander": "^2.9.0", "is-my-json-valid": "^2.12.4", "pinkie-promise": "^2.0.0"}, "bin": {"har-validator": "bin/har-validator"}, "engines": {"node": ">=0.10"}}, "node_modules/har-validator/node_modules/ansi-regex": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-2.1.1.tgz", "integrity": "sha512-TIGnTpdo+E3+pCyAluZvtED5p5wCqLdezCyhPZzKPcxvFplEt4i+W7OONCKgeZFT3+y5NZZfOOS/Bdcanm1MYA==", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/har-validator/node_modules/ansi-styles": {"version": "2.2.1", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-2.2.1.tgz", "integrity": "sha512-kmCevFghRiWM7HB5zTPULl4r9bVFSWjz62MhqizDGUrq2NWuNMQyuv4tHHoKJHs69M/MF64lEcHdYIocrdWQYA==", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/har-validator/node_modules/chalk": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/chalk/-/chalk-1.1.3.tgz", "integrity": "sha512-U3lRVLMSlsCfjqYPbLyVv11M9CPW4I728d6TCKMAOJueEeB9/8o+eSsMnxPJD+Q+K909sdESg7C+tIkoH6on1A==", "dev": true, "dependencies": {"ansi-styles": "^2.2.1", "escape-string-regexp": "^1.0.2", "has-ansi": "^2.0.0", "strip-ansi": "^3.0.0", "supports-color": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/har-validator/node_modules/commander": {"version": "2.20.3", "resolved": "https://registry.npmjs.org/commander/-/commander-2.20.3.tgz", "integrity": "sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ==", "dev": true}, "node_modules/har-validator/node_modules/has-ansi": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/has-ansi/-/has-ansi-2.0.0.tgz", "integrity": "sha512-C8vBJ8DwUCx19vhm7urhTuUsr4/IyP6l4VzNQDv+ryHQObW3TTTp9yB68WpYgRe2bbaGuZ/se74IqFeVnMnLZg==", "dev": true, "dependencies": {"ansi-regex": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/har-validator/node_modules/strip-ansi": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-3.0.1.tgz", "integrity": "sha512-VhumSSbBqDTP8p2ZLKj40UjBCV4+v8bUSEpUb4KjRgWk9pbqGF4REFj6KEagidb2f/M6AzC0EmFyDNGaw9OCzg==", "dev": true, "dependencies": {"ansi-regex": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/har-validator/node_modules/supports-color": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-2.0.0.tgz", "integrity": "sha512-KKNVtd6pCYgPIKU4cp2733HWYCpplQhddZLBUryaAHou723x+FRzQ5Df824Fj+IyyuiQTRoub4SnIFfIcrp70g==", "dev": true, "engines": {"node": ">=0.8.0"}}, "node_modules/has-ansi": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/has-ansi/-/has-ansi-0.1.0.tgz", "integrity": "sha512-1YsTg1fk2/6JToQhtZkArMkurq8UoWU1Qe0aR3VUHjgij4nOylSWLWAtBXoZ4/dXOmugfLGm1c+QhuD0JyedFA==", "dev": true, "dependencies": {"ansi-regex": "^0.2.0"}, "bin": {"has-ansi": "cli.js"}, "engines": {"node": ">=0.10.0"}}, "node_modules/has-color": {"version": "0.1.7", "resolved": "https://registry.npmjs.org/has-color/-/has-color-0.1.7.tgz", "integrity": "sha512-kaNz5OTAYYmt646Hkqw50/qyxP2vFnTVu5AQ1Zmk22Kk5+4Qx6BpO8+u7IKsML5fOsFk0ZT0AcCJNYwcvaLBvw==", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/hasha": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/hasha/-/hasha-2.2.0.tgz", "integrity": "sha512-jZ38TU/EBiGKrmyTNNZgnvCZHNowiRI4+w/I9noMlekHTZH3KyGgvJLmhSgykeAQ9j2SYPDosM0Bg3wHfzibAQ==", "dev": true, "dependencies": {"is-stream": "^1.0.1", "pinkie-promise": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/hawk": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/hawk/-/hawk-1.0.0.tgz", "integrity": "sha512-Sg+VzrI7TjUomO0rjD6UXawsj50ykn5sB/xKNW/IenxzRVyw/wt9A2FLzYpGL/r0QG5hyXY8nLx/2m8UutoDcg==", "deprecated": "This module moved to @hapi/hawk. Please make sure to switch over as this distribution is no longer supported and may contain bugs and critical security issues.", "dev": true, "optional": true, "dependencies": {"boom": "0.4.x", "cryptiles": "0.2.x", "hoek": "0.9.x", "sntp": "0.2.x"}, "engines": {"node": ">=0.8.0"}}, "node_modules/heap": {"version": "0.2.7", "resolved": "https://registry.npmjs.org/heap/-/heap-0.2.7.tgz", "integrity": "sha512-2bsegYkkHO+h/9MGbn6KWcE45cHZgPANo5LXF7EvWdT0yT2EguSVO1nDgU5c8+ZOPwp2vMNa7YFsJhVcDR9Sdg==", "dev": true}, "node_modules/hoek": {"version": "0.9.1", "resolved": "https://registry.npmjs.org/hoek/-/hoek-0.9.1.tgz", "integrity": "sha512-ZZ6eGyzGjyMTmpSPYVECXy9uNfqBR7x5CavhUaLOeD6W0vWK1mp/b7O3f86XE0Mtfo9rZ6Bh3fnuw9Xr8MF9zA==", "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial).", "dev": true, "engines": {"node": ">=0.8.0"}}, "node_modules/hooker": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/hooker/-/hooker-0.2.3.tgz", "integrity": "sha512-t+UerCsQviSymAInD01Pw+Dn/usmz1sRO+3Zk1+lx8eg+WKpD2ulcwWqHHL0+aseRBr+3+vIhiG1K1JTwaIcTA==", "dev": true, "engines": {"node": "*"}}, "node_modules/htmlparser2": {"version": "3.8.3", "resolved": "https://registry.npmjs.org/htmlparser2/-/htmlparser2-3.8.3.tgz", "integrity": "sha512-hBxEg3CYXe+rPIua8ETe7tmG3XDn9B0edOE/e9wH2nLczxzgdu0m0aNHY+5wFZiviLWLdANPJTssa92dMcXQ5Q==", "dev": true, "dependencies": {"domelementtype": "1", "domhandler": "2.3", "domutils": "1.5", "entities": "1.0", "readable-stream": "1.1"}}, "node_modules/http-proxy-agent": {"version": "0.2.7", "resolved": "https://registry.npmjs.org/http-proxy-agent/-/http-proxy-agent-0.2.7.tgz", "integrity": "sha512-9W3grrlsrW2kRGNRbGkBNVFx4voQS1H1TxWR60MVHKQ+rw+kRtA9JXVGQiiDgYsp315Ex5HPk+3it4lBNyk4WA==", "dev": true, "dependencies": {"agent-base": "~1.0.1", "debug": "2", "extend": "3"}}, "node_modules/http-proxy-agent/node_modules/debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "dev": true, "dependencies": {"ms": "2.0.0"}}, "node_modules/http-proxy-agent/node_modules/ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A==", "dev": true}, "node_modules/http-signature": {"version": "0.10.1", "resolved": "https://registry.npmjs.org/http-signature/-/http-signature-0.10.1.tgz", "integrity": "sha512-coK8uR5rq2IMj+Hen+sKPA5ldgbCc1/spPdKCL1Fw6h+D0s/2LzMcRK0Cqufs1h0ryx/niwBHGFu8HC3hwU+lA==", "dev": true, "optional": true, "dependencies": {"asn1": "0.1.11", "assert-plus": "^0.1.5", "ctype": "0.5.3"}, "engines": {"node": ">=0.8"}}, "node_modules/https-proxy-agent": {"version": "0.3.6", "resolved": "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-0.3.6.tgz", "integrity": "sha512-ZuLafAeUu97abfbpAO9Cwjl3slsx6yZ7apTYBNVtMdoDhlVzUhxXO0qh+Xxqc5FAm7oq747k2jjbICYJdEYShg==", "dev": true, "dependencies": {"agent-base": "~1.0.1", "debug": "2", "extend": "3"}}, "node_modules/https-proxy-agent/node_modules/debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "dev": true, "dependencies": {"ms": "2.0.0"}}, "node_modules/https-proxy-agent/node_modules/ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A==", "dev": true}, "node_modules/iconv-lite": {"version": "0.2.11", "resolved": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.2.11.tgz", "integrity": "sha512-KhmFWgaQZY83Cbhi+ADInoUQ8Etn6BG5fikM9syeOjQltvR45h7cRKJ/9uvQEuD61I3Uju77yYce0/LhKVClQw==", "dev": true, "engines": {"node": ">=0.4.0"}}, "node_modules/inherits": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz", "integrity": "sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==", "dev": true}, "node_modules/is-buffer": {"version": "1.1.6", "resolved": "https://registry.npmjs.org/is-buffer/-/is-buffer-1.1.6.tgz", "integrity": "sha512-NcdALwpXkTm5Zvvbk7owOUSvVvBKDgKP5/ewfXEznmQFfs4ZRmanOeKBTjRVjka3QFoN6XJ+9F3USqfHqTaU5w==", "dev": true}, "node_modules/is-my-ip-valid": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/is-my-ip-valid/-/is-my-ip-valid-1.0.1.tgz", "integrity": "sha512-jxc8cBcOWbNK2i2aTkCZP6i7wkHF1bqKFrwEHuN5Jtg5BSaZHUZQ/JTOJwoV41YvHnOaRyWWh72T/KvfNz9DJg==", "dev": true}, "node_modules/is-my-json-valid": {"version": "2.20.6", "resolved": "https://registry.npmjs.org/is-my-json-valid/-/is-my-json-valid-2.20.6.tgz", "integrity": "sha512-1J<PERSON>wulVNjx8UqkPE/bqDaxtH4PXCe/2VRh/y3p99heOV87HG4Id5/VfDswd+YiAfHcRTfDlWgISycnHuhZq1aw==", "dev": true, "dependencies": {"generate-function": "^2.0.0", "generate-object-property": "^1.1.0", "is-my-ip-valid": "^1.0.0", "jsonpointer": "^5.0.0", "xtend": "^4.0.0"}}, "node_modules/is-my-json-valid/node_modules/xtend": {"version": "4.0.2", "resolved": "https://registry.npmjs.org/xtend/-/xtend-4.0.2.tgz", "integrity": "sha512-LKYU1iAXJXUgAXn9URjiu+MWhyUXHsvfp7mcuYm9dSUKK0/CjtrUwFAxD82/mCWbtLsGjFIad0wIsod4zrTAEQ==", "dev": true, "engines": {"node": ">=0.4"}}, "node_modules/is-promise": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/is-promise/-/is-promise-1.0.1.tgz", "integrity": "sha512-mjWH5XxnhMA8cFnDchr6qRP9S/kLntKuEfIYku+PaN1CnS8v+OG9O/BKpRCVRJvpIkgAZm0Pf5Is3iSSOILlcg==", "dev": true}, "node_modules/is-property": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/is-property/-/is-property-1.0.2.tgz", "integrity": "sha512-Ks/IoX00TtClbGQr4TWXemAnktAQvYB7HzcCxDGqEZU6oCmb2INHuOoKxbtR+HFkmYWBKv/dOZtGRiAjDhj92g==", "dev": true}, "node_modules/is-stream": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/is-stream/-/is-stream-1.1.0.tgz", "integrity": "sha512-uQPm8kcs47jx38atAcWTVxyltQYoPT68y9aWYdV6yWXSyW8mzSat0TL6CiWdZeCdF3KrAvpVtnHbTv4RN+rqdQ==", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/is-typedarray": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-typedarray/-/is-typedarray-1.0.0.tgz", "integrity": "sha512-cyA56iCMHAh5CdzjJIa4aohJyeO1YbwLi3Jc35MmRU6poroFjIGZzUzupGiRPOjgHg9TLu43xbpwXk523fMxKA==", "dev": true}, "node_modules/isarray": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/isarray/-/isarray-0.0.1.tgz", "integrity": "sha512-D2S+3GLxWH+uhrNEcoh/fnmYeP8E8/zHl644d/jdA0g2uyXvy3sb0qxotE+ne0LtccHknQzWwZEzhak7oJ0COQ==", "dev": true}, "node_modules/isexe": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz", "integrity": "sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==", "dev": true}, "node_modules/isstream": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/isstream/-/isstream-0.1.2.tgz", "integrity": "sha512-Yljz7ffyPbrLpLngrMtZ7NduUgVvi6wG9RJ9IUcyCd59YQ911PBJphODUcbOVbqYfxe1wuYf/LJ8PauMRwsM/g==", "dev": true}, "node_modules/jade": {"version": "1.3.1", "resolved": "https://registry.npmjs.org/jade/-/jade-1.3.1.tgz", "integrity": "sha512-21JjsbK/7cBNTBLpkN+1WRTvA3mYc7SMQWYc025NRggDLkKKqHGCiBVmF9ND/i/STmzmROe8hfgfHzbdL50N5w==", "deprecated": "Jade has been renamed to pug, please install the latest version of pug instead of jade", "dev": true, "dependencies": {"character-parser": "1.2.0", "commander": "2.1.0", "constantinople": "~2.0.0", "mkdirp": "~0.3.5", "monocle": "1.1.51", "transformers": "2.1.0", "with": "~3.0.0"}, "bin": {"jade": "bin/jade.js"}}, "node_modules/jade/node_modules/commander": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/commander/-/commander-2.1.0.tgz", "integrity": "sha512-J2wnb6TKniXNOtoHS8TSrG9IOQluPrsmyAJ8oCUJOBmv+uLBCyPYAZkD2jFvw2DCzIXNnISIM01NIvr35TkBMQ==", "dev": true, "engines": {"node": ">= 0.6.x"}}, "node_modules/jade/node_modules/mkdirp": {"version": "0.3.5", "resolved": "https://registry.npmjs.org/mkdirp/-/mkdirp-0.3.5.tgz", "integrity": "sha512-8OCq0De/h9ZxseqzCH8Kw/Filf5pF/vMI6+BH7Lu0jXz2pqYCjTAQRolSxRIi+Ax+oCCjlxoJMP0YQ4XlrQNHg==", "deprecated": "Legacy versions of mkdirp are no longer supported. Please update to mkdirp 1.x. (Note that the API surface has changed to use Promises in 1.x.)", "dev": true}, "node_modules/js-yaml": {"version": "2.0.5", "resolved": "https://registry.npmjs.org/js-yaml/-/js-yaml-2.0.5.tgz", "integrity": "sha512-<PERSON><PERSON>KcIksckDBUhg2JS874xVouiPkywVUh4yyUmLCDe1Zg3bCd6M+F1eGPenPeHLc2XC8pp9G8bsuofK0NeEqRkA==", "dev": true, "dependencies": {"argparse": "~ 0.1.11", "esprima": "~ 1.0.2"}, "bin": {"js-yaml": "bin/js-yaml.js"}, "engines": {"node": ">= 0.6.0"}}, "node_modules/jsbn": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/jsbn/-/jsbn-0.1.1.tgz", "integrity": "sha512-UVU9dibq2JcFWxQPA6KCqj5O42VOmAY3zQUfEKxU0KpTGXwNoCjkX1e13eHNvw/xPynt6pU0rZ1htjWTNTSXsg==", "dev": true}, "node_modules/jscs": {"version": "1.5.9", "resolved": "https://registry.npmjs.org/jscs/-/jscs-1.5.9.tgz", "integrity": "sha512-La1FOS7EnsNqx+IC8qOnYxh3XC8xnzGnPGz7r6Co3mYU2awv6tOCfG6UpWGoEXmb9RD5xh5VI4g0cxg5uIn6jQ==", "deprecated": "JSCS has merged with ESLint! See - https://medium.com/@markelog/jscs-end-of-the-line-bc9bf0b3fdb2", "dev": true, "dependencies": {"colors": "~0.6.2", "commander": "~2.3.0", "esprima": "~1.2.2", "glob": "~4.0.0", "minimatch": "~0.4.0", "strip-json-comments": "~0.1.1", "supports-color": "~0.2.0", "vow": "~0.4.3", "vow-fs": "~0.3.1", "xmlbuilder": "~2.3.0"}, "bin": {"jscs": "bin/jscs"}, "engines": {"node": ">= 0.10.0"}}, "node_modules/jscs/node_modules/commander": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/commander/-/commander-2.3.0.tgz", "integrity": "sha512-CD452fnk0jQyk3NfnK+KkR/hUPoHt5pVaKHogtyyv3N0U4QfAal9W0/rXLOg/vVZgQKa7jdtXypKs1YAip11uQ==", "dev": true, "engines": {"node": ">= 0.6.x"}}, "node_modules/jscs/node_modules/esprima": {"version": "1.2.5", "resolved": "https://registry.npmjs.org/esprima/-/esprima-1.2.5.tgz", "integrity": "sha512-S9VbPDU0adFErpDai3qDkjq8+G05ONtKzcyNrPKg/ZKa+tf879nX2KexNU95b31UoTJjRLInNBHHHjFPoCd7lQ==", "dev": true, "bin": {"esparse": "bin/esparse.js", "esvalidate": "bin/esvalidate.js"}, "engines": {"node": ">=0.4.0"}}, "node_modules/jscs/node_modules/minimatch": {"version": "0.4.0", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-0.4.0.tgz", "integrity": "sha512-yJKJL1g3to7f4C/9LzHXTzNh550xKGefiCls9RS+DDdsDpKpndY49UDZW5sj/3yeac3Hl2Px3w5bT8bM/dMrWQ==", "deprecated": "Please update to minimatch 3.0.2 or higher to avoid a RegExp DoS issue", "dev": true, "dependencies": {"lru-cache": "2", "sigmund": "~1.0.0"}, "engines": {"node": "*"}}, "node_modules/jshint": {"version": "2.5.11", "resolved": "https://registry.npmjs.org/jshint/-/jshint-2.5.11.tgz", "integrity": "sha512-Xbxu1H+316lrkK+esbrYPHBLUlbLd0mrQO3hbOizsgh7Xe6AEhjbD6NZlRqNZJewdosKIOkU+TsPVcRyth0QmQ==", "dev": true, "dependencies": {"cli": "0.6.x", "console-browserify": "1.1.x", "exit": "0.1.x", "htmlparser2": "3.8.x", "minimatch": "1.0.x", "shelljs": "0.3.x", "strip-json-comments": "1.0.x", "underscore": "1.6.x"}, "bin": {"jshint": "bin/jshint"}}, "node_modules/jshint/node_modules/strip-json-comments": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-1.0.4.tgz", "integrity": "sha512-AOPG8EBc5wAikaG1/7uFCNFJwnKOuQwFTpYBdTW6OvWHeZBQBrAA/amefHGrEiOnCPcLFZK6FUPtWVKpQVIRgg==", "dev": true, "bin": {"strip-json-comments": "cli.js"}, "engines": {"node": ">=0.8.0"}}, "node_modules/jshint/node_modules/underscore": {"version": "1.6.0", "resolved": "https://registry.npmjs.org/underscore/-/underscore-1.6.0.tgz", "integrity": "sha512-z4o1fvKUojIWh9XuaVLUDdf86RQiq13AC1dmHbTpoyuu+bquHms76v16CjycCbec87J7z0k//SiQVk0sMdFmpQ==", "dev": true}, "node_modules/json-diff": {"version": "0.3.1", "resolved": "https://registry.npmjs.org/json-diff/-/json-diff-0.3.1.tgz", "integrity": "sha512-RmW5zs4FSOKCeF3ZiYLTYzq+gbm6JA01x27UHU16k7KlEhCWgE4FUK/rEPhq7C77VEebRKO6C7l+Tmtm1Er8ug==", "dev": true, "dependencies": {"cli-color": "~0.1.6", "difflib": "~0.2.1", "dreamopt": "~0.6.0"}, "bin": {"json-diff": "bin/json-diff.js"}, "engines": {"node": "*"}}, "node_modules/json-schema": {"version": "0.4.0", "resolved": "https://registry.npmjs.org/json-schema/-/json-schema-0.4.0.tgz", "integrity": "sha512-es94M3nTIfsEPisRafak+HDLfHXnKBhV3vU5eqPcS3flIWqcxJWgXHXiey3YrpaNsanY5ei1VoYEbOzijuq9BA==", "dev": true}, "node_modules/json-stringify-safe": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/json-stringify-safe/-/json-stringify-safe-5.0.1.tgz", "integrity": "sha512-ZClg6AaYvamvYEE82d3Iyd3vSSIjQ+odgjaTzRuO3s7toCdFKczob2i0zCh7JE8kWn17yvAWhUVxvqGwUalsRA==", "dev": true}, "node_modules/jsonfile": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/jsonfile/-/jsonfile-1.1.1.tgz", "integrity": "sha512-yrmo536TrJWDHNYlkCjtkPSqNnAkt6u1k89O5Xxr53zc4DkdikTHE+Fx6VF7syJd18xsdo24U4csedAkhLgxVQ==", "dev": true}, "node_modules/jsonify": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/jsonify/-/jsonify-0.0.1.tgz", "integrity": "sha512-2/Ki0GcmuqSrgFyelQq9M05y7PS0mEwuIzrf3f1fPqkVDVRvZrPZtVSMHxdgo8Aq0sxAOb/cr2aqqA3LeWHVPg==", "dev": true, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/jsonpointer": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/jsonpointer/-/jsonpointer-5.0.1.tgz", "integrity": "sha512-p/nXbhSEcu3pZRdkW1OfJhpsVtW1gd4Wa1fnQc9YLiTfAjn0312eMKimbdIQzuZl9aa9xUGaRlP9T/CJE/ditQ==", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/jsprim": {"version": "1.4.2", "resolved": "https://registry.npmjs.org/jsprim/-/jsprim-1.4.2.tgz", "integrity": "sha512-P2bSOMAc/ciLz6DzgjVlGJP9+BrJWu5UDGK70C2iweC5QBIeFf0ZXRvGjEj2uYgrY2MkAAhsSWHDWlFtEroZWw==", "dev": true, "dependencies": {"assert-plus": "1.0.0", "extsprintf": "1.3.0", "json-schema": "0.4.0", "verror": "1.10.0"}, "engines": {"node": ">=0.6.0"}}, "node_modules/jsprim/node_modules/assert-plus": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/assert-plus/-/assert-plus-1.0.0.tgz", "integrity": "sha512-NfJ4UzBCcQGLDlQq7nHxH+tv3kyZ0hHQqF5BO6J7tNJeP5do1llPr8dZ8zHonfhAu0PHAdMkSo+8o0wxg9lZWw==", "dev": true, "engines": {"node": ">=0.8"}}, "node_modules/kew": {"version": "0.7.0", "resolved": "https://registry.npmjs.org/kew/-/kew-0.7.0.tgz", "integrity": "sha512-IG6nm0+QtAMdXt9KvbgbGdvY50RSrw+U4sGZg+KlrSKPJEwVE5JVoI3d7RWfSMdBQneRheeAOj3lIjX5VL/9RQ==", "dev": true}, "node_modules/kind-of": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha512-NOW9QQXMoZGg/oqnVNoNTTIFEIid1627WCffUBJEdMxYApq7mNE7CpzucIPc+ZQg25Phej7IJSmX3hO+oblOtQ==", "dev": true, "dependencies": {"is-buffer": "^1.1.5"}, "engines": {"node": ">=0.10.0"}}, "node_modules/klaw": {"version": "1.3.1", "resolved": "https://registry.npmjs.org/klaw/-/klaw-1.3.1.tgz", "integrity": "sha512-TED5xi9gGQjGpNnvRWknrwAB1eL5GciPfVFOt3Vk1OJCVDQbzuSfrF3hkUQKlsgKrG1F+0t5W0m+Fje1jIt8rw==", "dev": true, "optionalDependencies": {"graceful-fs": "^4.1.9"}}, "node_modules/klaw/node_modules/graceful-fs": {"version": "4.2.11", "resolved": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz", "integrity": "sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==", "dev": true, "optional": true}, "node_modules/lazy-cache": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/lazy-cache/-/lazy-cache-1.0.4.tgz", "integrity": "sha512-RE2g0b5VGZsOCFOCgP7omTRYFqydmZkBwl5oNnQ1lDYC57uyO9KqNnNVxT7COSHTxrRCWVcAVOcbjk+tvh/rgQ==", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/less": {"version": "1.7.5", "resolved": "https://registry.npmjs.org/less/-/less-1.7.5.tgz", "integrity": "sha512-+cddvg94fFlyJUijF+jO8Dvrr1RWIAf3l/kXDUiQ65za+o468iA9jwal2dcKnmJTHTFqnQQGo2jT1pJqAlI73Q==", "dev": true, "bin": {"lessc": "bin/lessc"}, "engines": {"node": ">=0.8.0"}, "optionalDependencies": {"clean-css": "2.2.x", "graceful-fs": "~3.0.2", "mime": "~1.2.11", "mkdirp": "~0.5.0", "request": "~2.40.0", "source-map": "0.1.x"}}, "node_modules/less/node_modules/hawk": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/hawk/-/hawk-1.1.1.tgz", "integrity": "sha512-am8sVA2bCJIw8fuuVcKvmmNnGFUGW8spTkVtj2fXTEZVkfN42bwFZFtDem57eFi+NSxurJB8EQ7Jd3uCHLn8Vw==", "deprecated": "This module moved to @hapi/hawk. Please make sure to switch over as this distribution is no longer supported and may contain bugs and critical security issues.", "dev": true, "optional": true, "dependencies": {"boom": "0.4.x", "cryptiles": "0.2.x", "hoek": "0.9.x", "sntp": "0.2.x"}, "engines": {"node": ">=0.8.0"}}, "node_modules/less/node_modules/mime-types": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/mime-types/-/mime-types-1.0.2.tgz", "integrity": "sha512-echfutj/t5SoTL4WZpqjA1DCud1XO0WQF3/GJ48YBmc4ZMhCK77QA6Z/w6VTQERLKuJ4drze3kw2TUT8xZXVNw==", "dev": true, "optional": true, "engines": {"node": ">= 0.8.0"}}, "node_modules/less/node_modules/qs": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/qs/-/qs-1.0.2.tgz", "integrity": "sha512-tHuOP9TN/1VmDM/ylApGK1QF3PSIP8I6bHDEfoKNQeViREQ/sfu1bAUrA1hoDun8p8Tpm7jcsz47g+3PiGoYdg==", "dev": true, "optional": true}, "node_modules/less/node_modules/request": {"version": "2.40.0", "resolved": "https://registry.npmjs.org/request/-/request-2.40.0.tgz", "integrity": "sha512-waNoGB4Z7bPn+lgqPk7l7hhze4Vd68jKccnwLeS7vr9GMxz0iWQbYTbBNWzfIk87Urx7V44pu29qjF/omej+Fw==", "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "dev": true, "engines": ["node >= 0.8.0"], "optional": true, "dependencies": {"forever-agent": "~0.5.0", "json-stringify-safe": "~5.0.0", "mime-types": "~1.0.1", "node-uuid": "~1.4.0", "qs": "~1.0.0"}, "optionalDependencies": {"aws-sign2": "~0.5.0", "form-data": "~0.1.0", "hawk": "1.1.1", "http-signature": "~0.10.0", "oauth-sign": "~0.3.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0"}}, "node_modules/less/node_modules/tunnel-agent": {"version": "0.4.3", "resolved": "https://registry.npmjs.org/tunnel-agent/-/tunnel-agent-0.4.3.tgz", "integrity": "sha512-e0IoVDWx8SDHc/hwFTqJDQ7CCDTEeGhmcT9jkWJjoGQSpgBz20nAMr80E3Tpk7PatJ1b37DQDgJR3CNSzcMOZQ==", "dev": true, "optional": true, "engines": {"node": "*"}}, "node_modules/load-grunt-tasks": {"version": "0.6.0", "resolved": "https://registry.npmjs.org/load-grunt-tasks/-/load-grunt-tasks-0.6.0.tgz", "integrity": "sha512-OUMvXssHQjX1RZ24ouBboouj0joMjc8OArRMbXEKTIfCuGtAbMlvRUirXdJDKZePdwQRIT/pAWXarjo3YrPUaQ==", "dev": true, "dependencies": {"findup-sync": "^0.1.2", "multimatch": "^0.3.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/lodash": {"version": "0.9.2", "resolved": "https://registry.npmjs.org/lodash/-/lodash-0.9.2.tgz", "integrity": "sha512-LVbt/rjK62gSbhehDVKL0vlaime4Y1IBixL+bKeNfoY4L2zab/jGrxU6Ka05tMA/zBxkTk5t3ivtphdyYupczw==", "dev": true, "engines": ["node", "rhino"]}, "node_modules/lodash._arraypool": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash._arraypool/-/lodash._arraypool-2.4.1.tgz", "integrity": "sha512-tC2aLC7bbkDXKNrjDu9OLiVx9pFIvjinID2eD9PzNdAQGZScWUd/h8faqOw5d6oLsOvFRCRbz1ASoB+deyMVUw==", "dev": true}, "node_modules/lodash._basebind": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash._basebind/-/lodash._basebind-2.4.1.tgz", "integrity": "sha512-VGHm6DH+1UiuafQdE/DNMqxOcSyhRu0xO9+jPDq7xITRn5YOorGrHVQmavMVXCYmTm80YRTZZCn/jTW7MokwLg==", "dev": true, "dependencies": {"lodash._basecreate": "~2.4.1", "lodash._setbinddata": "~2.4.1", "lodash._slice": "~2.4.1", "lodash.isobject": "~2.4.1"}}, "node_modules/lodash._basecreate": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash._basecreate/-/lodash._basecreate-2.4.1.tgz", "integrity": "sha512-8JJ3FnMPm54t3BwPLk8q8mPyQKQXm/rt9df+awr4NGtyJrtcCXM3Of1I86S6jVy1b4yAyFBb8wbKPEauuqzRmQ==", "dev": true, "dependencies": {"lodash._isnative": "~2.4.1", "lodash.isobject": "~2.4.1", "lodash.noop": "~2.4.1"}}, "node_modules/lodash._basecreatecallback": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash._basecreatecallback/-/lodash._basecreatecallback-2.4.1.tgz", "integrity": "sha512-SLczhg860fGW7AKlYcuOFstDtJuQhaANlJ4Y/jrOoRxhmVtK41vbJDH3OefVRSRkSCQo4HI82QVkAVsoGa5gSw==", "dev": true, "dependencies": {"lodash._setbinddata": "~2.4.1", "lodash.bind": "~2.4.1", "lodash.identity": "~2.4.1", "lodash.support": "~2.4.1"}}, "node_modules/lodash._basecreatewrapper": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash._basecreatewrapper/-/lodash._basecreatewrapper-2.4.1.tgz", "integrity": "sha512-x2ja1fa/qmzbizuXgVM4QAP9svtMbdxjG8Anl9bCeDAwLOVQ1vLrA0hLb/NkpbGi9evjtkl0aWLTEoOlUdBPQA==", "dev": true, "dependencies": {"lodash._basecreate": "~2.4.1", "lodash._setbinddata": "~2.4.1", "lodash._slice": "~2.4.1", "lodash.isobject": "~2.4.1"}}, "node_modules/lodash._basemerge": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash._basemerge/-/lodash._basemerge-2.4.1.tgz", "integrity": "sha512-gONthd9UKDfp9RVkGF03CbSoRdeNiXP/oHACM2V+OU68/umJ3yvMh+R8PVv15X51jV8V3o/v9s/ZM62hM4zsoQ==", "dev": true, "dependencies": {"lodash.foreach": "~2.4.1", "lodash.forown": "~2.4.1", "lodash.isarray": "~2.4.1", "lodash.isplainobject": "~2.4.1"}}, "node_modules/lodash._createwrapper": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash._createwrapper/-/lodash._createwrapper-2.4.1.tgz", "integrity": "sha512-5TCfLt1haQpsa7bgLYRKNNE4yqhO4ZxIayN1btQmazMchO6Q8JYFRMqbJ3W+uNmMm4R0Jw7KGkZX5YfDDnywuw==", "dev": true, "dependencies": {"lodash._basebind": "~2.4.1", "lodash._basecreatewrapper": "~2.4.1", "lodash._slice": "~2.4.1", "lodash.isfunction": "~2.4.1"}}, "node_modules/lodash._getarray": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash._getarray/-/lodash._getarray-2.4.1.tgz", "integrity": "sha512-iIrScwY3atGvLVbQL/+CNUznaPwBJg78S/JO4cTUFXRkRsZgEBhscB27cVoT4tsIOUyFu/5M/0umfHNGJ6wYwg==", "dev": true, "dependencies": {"lodash._arraypool": "~2.4.1"}}, "node_modules/lodash._isnative": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash._isnative/-/lodash._isnative-2.4.1.tgz", "integrity": "sha512-B<PERSON>lKGKNHhCHswGOWtmVb5zBygyxN7EmTuzVOSQI6QSoGhG+kvv71gICFS1TBpnqvT1n53txK8CDK3u5D2/GZxQ==", "dev": true}, "node_modules/lodash._maxpoolsize": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash._maxpoolsize/-/lodash._maxpoolsize-2.4.1.tgz", "integrity": "sha512-xKDem1BxoIfcCtaJHotjtyfdIvZO9qrF+mv3G1+ngQmaI3MJt3Qm46i9HLk/CbzABbavUrr1/EomQT8KxtsrYA==", "dev": true}, "node_modules/lodash._objecttypes": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash._objecttypes/-/lodash._objecttypes-2.4.1.tgz", "integrity": "sha512-XpqGh1e7hhkOzftBfWE7zt+Yn9mVHFkDhicVttvKLsoCMLVVL+xTQjfjB4X4vtznauxv0QZ5ZAeqjvat0dh62Q==", "dev": true}, "node_modules/lodash._releasearray": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash._releasearray/-/lodash._releasearray-2.4.1.tgz", "integrity": "sha512-wwCwWX8PK/mYR5VZjcU5JFl6py/qrfLGMxzpKOfSqgA1PaZ6Z625CZLCxH1KsqyxSkOFmNm+mEYjeDpXlM4hrg==", "dev": true, "dependencies": {"lodash._arraypool": "~2.4.1", "lodash._maxpoolsize": "~2.4.1"}}, "node_modules/lodash._setbinddata": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash._setbinddata/-/lodash._setbinddata-2.4.1.tgz", "integrity": "sha512-Vx0XKzpg2DFbQw4wrp1xSWd2sfl3W/BG6bucSRZmftS1AzbWRemCmBQDxyQTNhlLNec428PXkuuja+VNBZgu2A==", "dev": true, "dependencies": {"lodash._isnative": "~2.4.1", "lodash.noop": "~2.4.1"}}, "node_modules/lodash._shimisplainobject": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash._shimisplainobject/-/lodash._shimisplainobject-2.4.1.tgz", "integrity": "sha512-1cXTOXHisjXYlhWNOIztDh1Bo9IXmxYEQXpCH6f/6wz2JV7bMfnA5gnIjTyvvs3WRCk8ohDUu0JtUrfU/VmNIg==", "dev": true, "dependencies": {"lodash.forin": "~2.4.1", "lodash.isfunction": "~2.4.1"}}, "node_modules/lodash._shimkeys": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash._shimkeys/-/lodash._shimkeys-2.4.1.tgz", "integrity": "sha512-lBrglYxLD/6KAJ8IEa5Lg+YHgNAL7FyKqXg4XOUI+Du/vtniLs1ZqS+yHNKPkK54waAgkdUnDOYaWf+rv4B+AA==", "dev": true, "dependencies": {"lodash._objecttypes": "~2.4.1"}}, "node_modules/lodash._slice": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash._slice/-/lodash._slice-2.4.1.tgz", "integrity": "sha512-+odPJa4PE2UgYnQgJgkLs0UD03QU78R2ivhrFnG9GdtYOZdE6ObxOj7KiUEUlqOOgatFT+ZqSypFjDSduTigKg==", "dev": true}, "node_modules/lodash.assign": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash.assign/-/lodash.assign-2.4.1.tgz", "integrity": "sha512-AqQ4AJz5buSx9ELXWt5dONwJyVPd4NTADMKhoVYWCugjoVf172/LpvVhwmSJn4g8/Dc0S8hxTe8rt5Dob3X9KQ==", "dev": true, "dependencies": {"lodash._basecreatecallback": "~2.4.1", "lodash._objecttypes": "~2.4.1", "lodash.keys": "~2.4.1"}}, "node_modules/lodash.bind": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash.bind/-/lodash.bind-2.4.1.tgz", "integrity": "sha512-hn2VWYZ+N9aYncRad4jORvlGgpFrn+axnPIWRvFxjk6CWcZH5b5alI8EymYsHITI23Z9wrW/+ORq+azrVFpOfw==", "dev": true, "dependencies": {"lodash._createwrapper": "~2.4.1", "lodash._slice": "~2.4.1"}}, "node_modules/lodash.create": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash.create/-/lodash.create-2.4.1.tgz", "integrity": "sha512-M90cJn9zpWHtOVMO8U2M6LW+5Quu850t67HYXTNRHalkNIEQL7bf4mns1PoqnFNjZ26SEi3QumZlnn8Pq6t1Ww==", "dev": true, "dependencies": {"lodash._basecreate": "~2.4.1", "lodash.assign": "~2.4.1"}}, "node_modules/lodash.foreach": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash.foreach/-/lodash.foreach-2.4.1.tgz", "integrity": "sha512-AvOobAkE7qBtIiHU5QHQIfveWH5Usr9pIcFIzBv7u4S6bvb3FWpFrh9ltqBY7UeL5lw6e8d+SggiUXQVyh+FpA==", "dev": true, "dependencies": {"lodash._basecreatecallback": "~2.4.1", "lodash.forown": "~2.4.1"}}, "node_modules/lodash.forin": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash.forin/-/lodash.forin-2.4.1.tgz", "integrity": "sha512-yUdUVtDH3HUqnNj8USzqoiadqBdDBQt5fpDsjeZgQtRYn4kuDTaz0KpXnSDDIJhiFQ/Nen8/+yWDD0By7XycXA==", "dev": true, "dependencies": {"lodash._basecreatecallback": "~2.4.1", "lodash._objecttypes": "~2.4.1"}}, "node_modules/lodash.forown": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash.forown/-/lodash.forown-2.4.1.tgz", "integrity": "sha512-VC+CKm/zSs5t3i/MHv71HZoQphuqOvez1xhjWBwHU5zAbsCYrqwHr+MyQyMk14HzA3hSRNA5lCqDMSw5G2Qscg==", "dev": true, "dependencies": {"lodash._basecreatecallback": "~2.4.1", "lodash._objecttypes": "~2.4.1", "lodash.keys": "~2.4.1"}}, "node_modules/lodash.identity": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash.identity/-/lodash.identity-2.4.1.tgz", "integrity": "sha512-VRYX+8XipeLjorag5bz3YBBRJ+5kj8hVBzfnaHgXPZAVTYowBdY5l0M5ZnOmlAMCOXBFabQtm7f5VqjMKEji0w==", "dev": true}, "node_modules/lodash.isarray": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash.isarray/-/lodash.isarray-2.4.1.tgz", "integrity": "sha512-yRDd0z+APziDqbk0MqR6Qfwj/Qn3jLxFJbI9U8MuvdTnqIXdZ5YXyGLnwuzCpZmjr26F1GNOjKLMMZ10i/wy6A==", "dev": true, "dependencies": {"lodash._isnative": "~2.4.1"}}, "node_modules/lodash.isempty": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash.isempty/-/lodash.isempty-2.4.1.tgz", "integrity": "sha512-tQ4W6Ngf/q5NbzjRyLvuTca+MSjIF6e9PTY6fS7Oah10LT3HXuUdMn5CR0TQG4VhKpIo0Xzi2IBBeRBmt8w+BQ==", "dev": true, "dependencies": {"lodash.forown": "~2.4.1", "lodash.isfunction": "~2.4.1"}}, "node_modules/lodash.isfunction": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash.isfunction/-/lodash.isfunction-2.4.1.tgz", "integrity": "sha512-6XcAB3izeQxPOQQNAJbbdjXbvWEt2Pn9ezPrjr4CwoLwmqsLVbsiEXD19cmmt4mbzOCOCdHzOQiUivUOJLra7w==", "dev": true}, "node_modules/lodash.isobject": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash.isobject/-/lodash.isobject-2.4.1.tgz", "integrity": "sha512-sTebg2a1PoicYEZXD5PBdQcTlIJ6hUslrlWr7iV0O7n+i4596s2NQ9I5CaZ5FbXSfya/9WQsrYLANUJv9paYVA==", "dev": true, "dependencies": {"lodash._objecttypes": "~2.4.1"}}, "node_modules/lodash.isplainobject": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash.isplainobject/-/lodash.isplainobject-2.4.1.tgz", "integrity": "sha512-2giZfjfnDTJbU/WOB8vzWvO6/8+6btWKwvbZZirewRf+SEGr8CiygksV1BhIv6XEmGYAoGXydq5Ekg9S21q0QQ==", "dev": true, "dependencies": {"lodash._isnative": "~2.4.1", "lodash._shimisplainobject": "~2.4.1"}}, "node_modules/lodash.keys": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash.keys/-/lodash.keys-2.4.1.tgz", "integrity": "sha512-ZpJhwvUXHSNL5wYd1RM6CUa2ZuqorG9ngoJ9Ix5Cce+uX7I5O/E06FCJdhSZ33b5dVyeQDnIlWH7B2s5uByZ7g==", "dev": true, "dependencies": {"lodash._isnative": "~2.4.1", "lodash._shimkeys": "~2.4.1", "lodash.isobject": "~2.4.1"}}, "node_modules/lodash.merge": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash.merge/-/lodash.merge-2.4.1.tgz", "integrity": "sha512-xalyWXgl3fJISOlUizOxFpJwG+XUj3dGbCgJ9VKuf66R37v2h8PRwSAzpUGhoHwLhInGGy86GM4ZX3Gl6ohYNw==", "dev": true, "dependencies": {"lodash._basecreatecallback": "~2.4.1", "lodash._basemerge": "~2.4.1", "lodash._getarray": "~2.4.1", "lodash._releasearray": "~2.4.1", "lodash._slice": "~2.4.1", "lodash.isobject": "~2.4.1"}}, "node_modules/lodash.noop": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash.noop/-/lodash.noop-2.4.1.tgz", "integrity": "sha512-uNcV98/blRhInPUGQEnj9ekXXfG+q+rfoNSFZgl/eBfog9yBDW9gfUv2AHX/rAF7zZRlzWhbslGhbGQFZlCkZA==", "dev": true}, "node_modules/lodash.support": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/lodash.support/-/lodash.support-2.4.1.tgz", "integrity": "sha512-6SwqWwGFHhTXEiqB/yQgu8FYd//tm786d49y7kizHVCJH7zdzs191UQn3ES3tkkDbUddNRfkCRYqJFHtbLnbCw==", "dev": true, "dependencies": {"lodash._isnative": "~2.4.1"}}, "node_modules/longest": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/longest/-/longest-1.0.1.tgz", "integrity": "sha512-k+yt5n3l48JU4k8ftnKG6V7u32wyH2NfKzeMto9F/QRE0amxy/LayxwlvjjkZEIzqR+19IrtFO8p5kB9QaYUFg==", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/lru-cache": {"version": "2.7.3", "resolved": "https://registry.npmjs.org/lru-cache/-/lru-cache-2.7.3.tgz", "integrity": "sha512-W<PERSON>bWJ60c3AgAz8a2iYErDrcT2C7OmKnsWhIcHOjkUHFjkXncJhtLxNSqUmxRxRunpb5I8Vprd7aNSd2NtksJQ==", "dev": true}, "node_modules/markdown": {"version": "0.5.0", "resolved": "https://registry.npmjs.org/markdown/-/markdown-0.5.0.tgz", "integrity": "sha512-ctGPIcuqsYoJ493sCtFK7H4UEgMWAUdXeBhPbdsg1W0LsV9yJELAHRsMmWfTgao6nH0/x5gf9FmsbxiXnrgaIQ==", "dev": true, "dependencies": {"nopt": "~2.1.1"}, "bin": {"md2html": "bin/md2html.js"}, "engines": {"node": "*"}}, "node_modules/markdown/node_modules/nopt": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/nopt/-/nopt-2.1.2.tgz", "integrity": "sha512-x8vXm7BZ2jE1Txrxh/hO74HTuYZQEbo8edoRcANgdZ4+PCV+pbjd/xdummkmjjC7LU5EjPzlu8zEq/oxWylnKA==", "dev": true, "dependencies": {"abbrev": "1"}, "bin": {"nopt": "bin/nopt.js"}}, "node_modules/marked": {"version": "0.3.19", "resolved": "https://registry.npmjs.org/marked/-/marked-0.3.19.tgz", "integrity": "sha512-ea2eGWOqNxPcXv8dyERdSr/6FmzvWwzjMxpfGB/sbMccXoct+xY+YukPD+QTUZwyvK7BZwcr4m21WBOW41pAkg==", "dev": true, "bin": {"marked": "bin/marked"}, "engines": {"node": ">=0.10.0"}}, "node_modules/maxmin": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/maxmin/-/maxmin-0.2.2.tgz", "integrity": "sha512-JZV7hp+e52/AaOkJVht/iorFiXW3+JKBbzbR1f7goiNZCTxTi131ciVmQefGLuVtkIM01bGC7EczsYmIG3baEw==", "dev": true, "dependencies": {"chalk": "^0.5.0", "figures": "^1.0.1", "gzip-size": "^0.2.0", "pretty-bytes": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/maxmin/node_modules/ansi-styles": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-1.1.0.tgz", "integrity": "sha512-f2PKUkN5QngiSemowa6Mrk9MPCdtFiOSmibjZ+j1qhLGHHYsqZwmBMRF3IRMVXo8sybDqx2fJl2d/8OphBoWkA==", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/maxmin/node_modules/chalk": {"version": "0.5.1", "resolved": "https://registry.npmjs.org/chalk/-/chalk-0.5.1.tgz", "integrity": "sha512-bIKA54hP8iZhyDT81TOsJiQvR1gW+ZYSXFaZUAvoD4wCHdbHY2actmpTE4x344ZlFqHbvoxKOaESULTZN2gstg==", "dev": true, "dependencies": {"ansi-styles": "^1.1.0", "escape-string-regexp": "^1.0.0", "has-ansi": "^0.1.0", "strip-ansi": "^0.3.0", "supports-color": "^0.2.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/maxmin/node_modules/strip-ansi": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-0.3.0.tgz", "integrity": "sha512-DerhZL7j6i6/nEnVG0qViKXI0OKouvvpsAiaj7c+LfqZZZxdwZtv8+UiA/w4VUJpT8UzX0pR1dcHOii1GbmruQ==", "dev": true, "dependencies": {"ansi-regex": "^0.2.1"}, "bin": {"strip-ansi": "cli.js"}, "engines": {"node": ">=0.10.0"}}, "node_modules/method-override": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/method-override/-/method-override-2.0.2.tgz", "integrity": "sha512-VdXhehVbkQcJD4MJisBqFjCGLlCQ5bhVkJqT9VpSgXyCccskmEYn/MA52pnDlqqffmkFazjGbFEwZFKwOIAKXg==", "dev": true, "dependencies": {"methods": "1.0.1", "parseurl": "1.0.1", "vary": "0.1.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/methods": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/methods/-/methods-1.0.1.tgz", "integrity": "sha512-2403MfnVypWSNIEpmQ26/ObZ5kSUx37E8NHRvriw0+I8Sne7k0HGuLGCk0OrCqURh4UIygD0cSsYq+Ll+kzNqA==", "dev": true}, "node_modules/mime": {"version": "1.2.11", "resolved": "https://registry.npmjs.org/mime/-/mime-1.2.11.tgz", "integrity": "sha512-Ysa2F/nqTNGHhhm9MV8ure4+Hc+Y8AWiqUdHxsO7xu8zc92ND9f3kpALHjaP026Ft17UfxrMt95c50PLUeynBw==", "dev": true}, "node_modules/mime-db": {"version": "1.52.0", "resolved": "https://registry.npmjs.org/mime-db/-/mime-db-1.52.0.tgz", "integrity": "sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==", "dev": true, "engines": {"node": ">= 0.6"}}, "node_modules/mime-types": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/mime-types/-/mime-types-1.0.0.tgz", "integrity": "sha512-aP3BmIq4ZAPJt6KywU5HbiG0UwCTHZA2JWHO9aLaxyr8OhPOiK4RPSZcS6TDS7zNzGDC3AACnq/XTuEsd/M1Kg==", "dev": true}, "node_modules/minimatch": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-1.0.0.tgz", "integrity": "sha512-Ejh5Odk/uFXAj5nf/NSXk0UamqcGAfOdHI7nY0zvCHyn4f3nKLFoUTp+lYxDxSih/40uW8lpwDplOWHdWkQXWA==", "deprecated": "Please update to minimatch 3.0.2 or higher to avoid a RegExp DoS issue", "dev": true, "dependencies": {"lru-cache": "2", "sigmund": "~1.0.0"}, "engines": {"node": "*"}}, "node_modules/minimist": {"version": "1.2.8", "resolved": "https://registry.npmjs.org/minimist/-/minimist-1.2.8.tgz", "integrity": "sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==", "dev": true, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/mkdirp": {"version": "0.5.6", "resolved": "https://registry.npmjs.org/mkdirp/-/mkdirp-0.5.6.tgz", "integrity": "sha512-FP+p8RB8OWpF3YZBCrP5gtADmtXApB5AMLn+vdyA+PyxCjrCs00mjyUozssO33cwDeT3wNGdLxJ5M//YqtHAJw==", "dev": true, "dependencies": {"minimist": "^1.2.6"}, "bin": {"mkdirp": "bin/cmd.js"}}, "node_modules/monocle": {"version": "1.1.51", "resolved": "https://registry.npmjs.org/monocle/-/monocle-1.1.51.tgz", "integrity": "sha512-G2ozHlK2+Z+oAvKSJ4pN0a7pLvCOWMDuHgtdARDt1uRyak9klMwg71ac37SWeULSzrkV0N75A0z9JaYtqXji1w==", "dev": true, "dependencies": {"readdirp": "~0.2.3"}}, "node_modules/morgan": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/morgan/-/morgan-1.1.1.tgz", "integrity": "sha512-Jx1pZHnbZ43TFAeY0NVuLqpeXX0O2aL7todwFModvpjZCGR+vBTKH0wOKQjwK1wgO/cERhFISIf4roSj1fx5Jg==", "dev": true, "dependencies": {"bytes": "1.0.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/ms": {"version": "0.6.2", "resolved": "https://registry.npmjs.org/ms/-/ms-0.6.2.tgz", "integrity": "sha512-/pc3eh7TWorTtbvXg8je4GvrvEqCfH7PA3P7iW01yL2E53FKixzgMBaQi0NOPbMJqY34cBSvR0tZtmlTkdUG4A==", "dev": true}, "node_modules/msee": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/msee/-/msee-0.1.2.tgz", "integrity": "sha512-x85F5MY0FjQpj8svpn9PZno6EpxgUIxP50Gs4kg57iyBbXO5IHavixsK2ZJgCUI4q6fNoFNdUxxEpabsucLZsQ==", "dev": true, "dependencies": {"cardinal": "^0.5.0", "chalk": "~0.4.0", "marked": "~0.3.0", "nopt": "~2.1.1", "xtend": "~2.1.1"}, "bin": {"msee": "bin/msee"}}, "node_modules/msee/node_modules/nopt": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/nopt/-/nopt-2.1.2.tgz", "integrity": "sha512-x8vXm7BZ2jE1Txrxh/hO74HTuYZQEbo8edoRcANgdZ4+PCV+pbjd/xdummkmjjC7LU5EjPzlu8zEq/oxWylnKA==", "dev": true, "dependencies": {"abbrev": "1"}, "bin": {"nopt": "bin/nopt.js"}}, "node_modules/multimatch": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/multimatch/-/multimatch-0.3.0.tgz", "integrity": "sha512-HYWM2J/yy3bE/0WjH2ZdOs18tEnXahpjZh+V3FSGSr+TPIOzlp3IgX+SqWW1i24emWpFfBp9rbYX8L+oMhU8Sg==", "dev": true, "dependencies": {"array-differ": "^0.1.0", "array-union": "^0.1.0", "minimatch": "^0.3.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/multimatch/node_modules/minimatch": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-0.3.0.tgz", "integrity": "sha512-WFX1jI1AaxNTZVOHLBVazwTWKaQjoykSzCBNXB72vDTCzopQGtyP91tKdFK5cv1+qMwPyiTu1HqUriqplI8pcA==", "deprecated": "Please update to minimatch 3.0.2 or higher to avoid a RegExp DoS issue", "dev": true, "dependencies": {"lru-cache": "2", "sigmund": "~1.0.0"}, "engines": {"node": "*"}}, "node_modules/multiparty": {"version": "3.2.8", "resolved": "https://registry.npmjs.org/multiparty/-/multiparty-3.2.8.tgz", "integrity": "sha512-F+7z2Jzr0OlVpF+XTfQ68CjkRLjMdSHRY+GiQhaAZKeRh48qkz97ds3s29dMFNqz2d/9xEOkofnaCj6YuX21KA==", "dev": true, "dependencies": {"readable-stream": "~1.1.9", "stream-counter": "~0.2.0"}, "engines": {"node": ">=0.8.0"}}, "node_modules/natives": {"version": "1.1.6", "resolved": "https://registry.npmjs.org/natives/-/natives-1.1.6.tgz", "integrity": "sha512-6+TDFewD4yxY14ptjKaS63GVdtKiES1pTPyxn9Jb0rBqPMZ7VcCiooEhPNsr+mqHtMGxa/5c/HhcC4uPEUw/nA==", "deprecated": "This module relies on Node.js's internals and will break at some point. Do not use it, and update to graceful-fs@4.x.", "dev": true}, "node_modules/ncp": {"version": "0.5.1", "resolved": "https://registry.npmjs.org/ncp/-/ncp-0.5.1.tgz", "integrity": "sha512-l+pJxuLlzwp11Dy72MJgCPNwIbXdv6imaACLiEMb2TIDyr54qz+nAZeD5qDlJefveaJ+R9Ug6KuozCxRpQXO0Q==", "dev": true, "bin": {"ncp": "bin/ncp"}}, "node_modules/negotiator": {"version": "0.4.6", "resolved": "https://registry.npmjs.org/negotiator/-/negotiator-0.4.6.tgz", "integrity": "sha512-nkhZDoiMZOCbMRPfDAilhyb8sETDhHP+zDCUv+JD26OSPOrYG+/76uooeqz3WTVh7BvQE41VV0YMTGKUgn9GQg==", "dev": true, "engines": {"node": "*"}}, "node_modules/node-uuid": {"version": "1.4.8", "resolved": "https://registry.npmjs.org/node-uuid/-/node-uuid-1.4.8.tgz", "integrity": "sha512-TkCET/3rr9mUuRp+CpO7qfgT++aAxfDRaalQhwPFzI9BY/2rCDn6OfpZOVggi1AXfTPpfkTrg5f5WQx5G1uLxA==", "deprecated": "Use uuid module instead", "dev": true, "bin": {"uuid": "bin/uuid"}}, "node_modules/nomnom": {"version": "1.6.2", "resolved": "https://registry.npmjs.org/nomnom/-/nomnom-1.6.2.tgz", "integrity": "sha512-mscrcqifc/QKP6/afmtoC84/mK6SKcDTDEfKPMSgJKeV5dtshiw5+AF90uwHyAqHkMIYIEcGkSAJnV6+T9PY/g==", "deprecated": "Package no longer supported. Contact <EMAIL> for more info.", "dev": true, "dependencies": {"colors": "0.5.x", "underscore": "~1.4.4"}}, "node_modules/nomnom/node_modules/colors": {"version": "0.5.1", "resolved": "https://registry.npmjs.org/colors/-/colors-0.5.1.tgz", "integrity": "sha512-XjsuUwpDeY98+yz959OlUK6m7mLBM+1MEG5oaenfuQnNnrQk1WvtcvFgN3FNDP3f2NmZ211t0mNEfSEN1h0eIg==", "dev": true, "engines": {"node": ">=0.1.90"}}, "node_modules/nomnom/node_modules/underscore": {"version": "1.4.4", "resolved": "https://registry.npmjs.org/underscore/-/underscore-1.4.4.tgz", "integrity": "sha512-ZqGrAgaqqZM7LGRzNjLnw5elevWb5M8LEoDMadxIW3OWbcv72wMMgKdwOKpd5Fqxe8choLD8HN3iSj3TUh/giQ==", "dev": true}, "node_modules/nopt": {"version": "1.0.10", "resolved": "https://registry.npmjs.org/nopt/-/nopt-1.0.10.tgz", "integrity": "sha512-NWmpvLSqUrgrAC9HCuxEvb+PSloHpqVu+FqcO4eeF2h5qYRhA7ev6KvelyQAKtegUbC6RypJnlEOhd8vloNKYg==", "dev": true, "dependencies": {"abbrev": "1"}, "bin": {"nopt": "bin/nopt.js"}, "engines": {"node": "*"}}, "node_modules/noptify": {"version": "0.0.3", "resolved": "https://registry.npmjs.org/noptify/-/noptify-0.0.3.tgz", "integrity": "sha512-EZT35r9AuK+hig+iYv4144kwfDEDhlj3zncVHw9b9d86TUYk/67BtBApkfPD1kslAT/8TTD262xdsVbV+iCSTw==", "dev": true, "dependencies": {"nopt": "~2.0.0"}}, "node_modules/noptify/node_modules/nopt": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/nopt/-/nopt-2.0.0.tgz", "integrity": "sha512-uVTsuT8Hm3aN3VttY+BPKw4KU9lVpI0F22UAr/I1r6+kugMr3oyhMALkycikLcdfvGRsgzCYN48DYLBFcJEUVg==", "dev": true, "dependencies": {"abbrev": "1"}, "bin": {"nopt": "bin/nopt.js"}}, "node_modules/npm": {"version": "1.4.21", "resolved": "https://registry.npmjs.org/npm/-/npm-1.4.21.tgz", "integrity": "sha512-9hHSihVS5Xa0lQzYHuhW0BLz3Wq1ep2yWhDKxI5FAMatUj20HQ9WTufrt4/3oNrhD6krSLaBcqIn7oYhZaI/rQ==", "bundleDependencies": ["abbrev", "ansi", "ansicolors", "ansistyles", "archy", "block-stream", "char-spinner", "child-process-close", "chmodr", "chownr", "cmd-shim", "columnify", "editor", "fstream", "fstream-npm", "github-url-from-git", "github-url-from-username-repo", "glob", "graceful-fs", "inflight", "inherits", "ini", "init-package-json", "lockfile", "lru-cache", "minimatch", "mkdirp", "node-gyp", "nopt", "npm-cache-filename", "npm-install-checks", "npm-registry-client", "npm-user-validate", "npmconf", "npmlog", "once", "opener", "osenv", "path-is-inside", "read", "read-installed", "read-package-json", "request", "retry", "<PERSON><PERSON><PERSON>", "semver", "sha", "slide", "sorted-object", "tar", "text-table", "uid-number", "which"], "dev": true, "dependencies": {"abbrev": "~1.0.5", "ansi": "~0.3.0", "ansicolors": "~0.3.2", "ansistyles": "~0.1.3", "archy": "0", "block-stream": "0.0.7", "char-spinner": "~1.0.1", "child-process-close": "~0.1.1", "chmodr": "~0.1.0", "chownr": "0", "cmd-shim": "~1.1.1", "columnify": "~1.1.0", "editor": "~0.1.0", "fstream": "~0.1.28", "fstream-npm": "~0.1.7", "github-url-from-git": "1.1.1", "github-url-from-username-repo": "~0.2.0", "glob": "~4.0.3", "graceful-fs": "~3.0.0", "inflight": "~1.0.1", "ini": "~1.2.0", "init-package-json": "~0.1.0", "lockfile": "~0.4.0", "lru-cache": "~2.5.0", "minimatch": "~0.3.0", "mkdirp": "~0.3.5", "node-gyp": "~0.13.0", "nopt": "~3.0.1", "npm-cache-filename": "~1.0.1", "npm-install-checks": "~1.0.2", "npm-registry-client": "~2.0.3", "npm-user-validate": "~0.1.0", "npmconf": "~1.1.4", "npmlog": "~0.1.1", "once": "~1.3.0", "opener": "~1.3.0", "osenv": "~0.1.0", "path-is-inside": "~1.0.0", "read": "~1.0.4", "read-installed": "~2.0.5", "read-package-json": "~1.2.2", "request": "~2.30.0", "retry": "~0.6.0", "rimraf": "~2.2.8", "semver": "~2.3.0", "sha": "~1.2.1", "slide": "~1.1.5", "sorted-object": "~1.0.0", "tar": "~0.1.20", "text-table": "~0.2.0", "uid-number": "0.0.5", "which": "1"}, "bin": {"npm": "bin/npm-cli.js"}, "engines": {"node": ">=0.8", "npm": "1"}}, "node_modules/npm-shrinkwrap": {"version": "3.1.8", "resolved": "https://registry.npmjs.org/npm-shrinkwrap/-/npm-shrinkwrap-3.1.8.tgz", "integrity": "sha512-u19lQncfxyUbfNeLsKdoB66iFLDWWqu60z1AW+IPMsEsetvgNldVxmRYoXxHtEL30GwpbgjuYaLLAEPDctnh2Q==", "dev": true, "dependencies": {"array-find": "^0.1.1", "error": "^3.0.0", "json-diff": "^0.3.1", "minimist": "0.0.8", "msee": "^0.1.1", "npm": "1.4.21", "read-json": "0.0.0", "rimraf": "^2.2.8", "run-parallel": "^0.3.0", "run-series": "^1.0.2", "safe-json-parse": "^1.0.1", "semver": "^2.2.1", "sorted-object": "^1.0.0", "string-template": "^0.1.3"}, "bin": {"npm-shrinkwrap": "bin/cli.js"}}, "node_modules/npm-shrinkwrap/node_modules/minimist": {"version": "0.0.8", "resolved": "https://registry.npmjs.org/minimist/-/minimist-0.0.8.tgz", "integrity": "sha512-miQKw5Hv4NS1Psg2517mV4e4dYNaO3++hjAvLOAzKqZ61rH8NS1SK+vbfBWZ5PY/Me/bEWhUwqMghEW5Fb9T7Q==", "dev": true}, "node_modules/npm-shrinkwrap/node_modules/semver": {"version": "2.3.2", "resolved": "https://registry.npmjs.org/semver/-/semver-2.3.2.tgz", "integrity": "sha512-abLdIKCosKfpnmhS52NCTjO4RiLspDfsn37prjzGrp9im5DPJOgh82Os92vtwGh6XdQryKI/7SREZnV+aqiXrA==", "dev": true, "bin": {"semver": "bin/semver"}}, "node_modules/npm/node_modules/abbrev": {"version": "1.0.5", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/ansi": {"version": "0.3.0", "dev": true, "inBundle": true}, "node_modules/npm/node_modules/ansicolors": {"version": "0.3.2", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/ansistyles": {"version": "0.1.3", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/archy": {"version": "0.0.2", "dev": true, "inBundle": true, "license": "MIT/X11", "engines": {"node": "*"}}, "node_modules/npm/node_modules/block-stream": {"version": "0.0.7", "dev": true, "inBundle": true, "license": "BSD", "dependencies": {"inherits": "~2.0.0"}, "engines": {"node": "0.4 || >=0.5.8"}}, "node_modules/npm/node_modules/char-spinner": {"version": "1.0.1", "dev": true, "inBundle": true, "license": "ISC"}, "node_modules/npm/node_modules/child-process-close": {"version": "0.1.1", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/chmodr": {"version": "0.1.0", "dev": true, "inBundle": true, "license": "BSD"}, "node_modules/npm/node_modules/chownr": {"version": "0.0.1", "dev": true, "inBundle": true, "license": "BSD", "engines": {"node": "*"}}, "node_modules/npm/node_modules/cmd-shim": {"version": "1.1.1", "dev": true, "inBundle": true, "license": "BSD", "dependencies": {"mkdirp": "~0.3.3"}, "optionalDependencies": {"graceful-fs": "2 || 3"}}, "node_modules/npm/node_modules/columnify": {"version": "1.1.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"strip-ansi": "^0.2.1", "wcwidth.js": "~0.0.4"}}, "node_modules/npm/node_modules/columnify/node_modules/strip-ansi": {"version": "0.2.2", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"ansi-regex": "^0.1.0"}, "bin": {"strip-ansi": "cli.js"}, "engines": {"node": ">=0.10.0"}}, "node_modules/npm/node_modules/columnify/node_modules/strip-ansi/node_modules/ansi-regex": {"version": "0.1.0", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npm/node_modules/columnify/node_modules/wcwidth.js": {"version": "0.0.4", "dev": true, "inBundle": true, "dependencies": {"underscore": ">= 1.3.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/npm/node_modules/columnify/node_modules/wcwidth.js/node_modules/underscore": {"version": "1.6.0", "dev": true, "inBundle": true}, "node_modules/npm/node_modules/editor": {"version": "0.1.0", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/fstream": {"version": "0.1.28", "dev": true, "inBundle": true, "license": "BSD", "dependencies": {"graceful-fs": "~3.0.2", "inherits": "~2.0.0", "mkdirp": "0.3", "rimraf": "2"}, "engines": {"node": ">=0.6"}}, "node_modules/npm/node_modules/fstream-npm": {"version": "0.1.7", "dev": true, "inBundle": true, "license": "BSD", "dependencies": {"fstream-ignore": "~0.0", "inherits": "2"}}, "node_modules/npm/node_modules/fstream-npm/node_modules/fstream-ignore": {"version": "0.0.8", "dev": true, "inBundle": true, "license": "BSD", "dependencies": {"fstream": "~0.1.17", "inherits": "2", "minimatch": "^0.3.0"}}, "node_modules/npm/node_modules/github-url-from-git": {"version": "1.1.1", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/github-url-from-username-repo": {"version": "0.2.0", "dev": true, "inBundle": true, "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/npm/node_modules/glob": {"version": "4.0.3", "dev": true, "inBundle": true, "license": "BSD", "dependencies": {"inherits": "2", "minimatch": "^0.3.0", "once": "^1.3.0"}, "engines": {"node": "*"}, "optionalDependencies": {"graceful-fs": "^3.0.2"}}, "node_modules/npm/node_modules/graceful-fs": {"version": "3.0.2", "dev": true, "inBundle": true, "license": "BSD", "engines": {"node": ">=0.4.0"}}, "node_modules/npm/node_modules/inflight": {"version": "1.0.1", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"once": "^1.3.0"}}, "node_modules/npm/node_modules/inherits": {"version": "2.0.1", "dev": true, "inBundle": true, "license": "ISC"}, "node_modules/npm/node_modules/ini": {"version": "1.2.1", "dev": true, "inBundle": true, "engines": {"node": "*"}}, "node_modules/npm/node_modules/init-package-json": {"version": "0.1.0", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"glob": "^4.0.2", "promzard": "~0.2.0", "read": "~1.0.1", "read-package-json": "1", "semver": "2.x"}}, "node_modules/npm/node_modules/init-package-json/node_modules/promzard": {"version": "0.2.2", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"read": "1"}}, "node_modules/npm/node_modules/lockfile": {"version": "0.4.2", "dev": true, "inBundle": true, "license": "BSD"}, "node_modules/npm/node_modules/lru-cache": {"version": "2.5.0", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/minimatch": {"version": "0.3.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"lru-cache": "2", "sigmund": "~1.0.0"}, "engines": {"node": "*"}}, "node_modules/npm/node_modules/minimatch/node_modules/sigmund": {"version": "1.0.0", "dev": true, "inBundle": true, "license": "BSD"}, "node_modules/npm/node_modules/mkdirp": {"version": "0.3.5", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/node-gyp": {"version": "0.13.1", "dev": true, "inBundle": true, "dependencies": {"fstream": "0", "glob": "3 || 4", "graceful-fs": "2||3", "minimatch": "0", "mkdirp": "0", "nopt": "2 || 3", "npmlog": "0", "osenv": "0", "request": "2", "rimraf": "2", "semver": "2", "tar": "0", "which": "1"}, "bin": {"node-gyp": "bin/node-gyp.js"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/npm/node_modules/nopt": {"version": "3.0.1", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"abbrev": "1"}, "bin": {"nopt": "bin/nopt.js"}}, "node_modules/npm/node_modules/npm-cache-filename": {"version": "1.0.1", "dev": true, "inBundle": true, "license": "ISC"}, "node_modules/npm/node_modules/npm-install-checks": {"version": "1.0.2", "dev": true, "inBundle": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"npmlog": "0.1", "semver": "^2.3.0"}}, "node_modules/npm/node_modules/npm-registry-client": {"version": "2.0.3", "dev": true, "inBundle": true, "license": "BSD", "dependencies": {"chownr": "0", "graceful-fs": "^3.0.0", "mkdirp": "~0.3.3", "npm-cache-filename": "^1.0.0", "request": "2 >=2.25.0", "retry": "0.6.0", "rimraf": "~2", "semver": "2 >=2.2.1", "slide": "~1.1.3"}, "optionalDependencies": {"npmlog": ""}}, "node_modules/npm/node_modules/npm-user-validate": {"version": "0.1.0", "dev": true, "inBundle": true, "license": "BSD"}, "node_modules/npm/node_modules/npmconf": {"version": "1.1.4", "dev": true, "inBundle": true, "license": "BSD", "dependencies": {"config-chain": "~1.1.8", "inherits": "~2.0.0", "ini": "^1.2.0", "mkdirp": "~0.3.3", "nopt": "~3.0.1", "once": "~1.3.0", "osenv": "^0.1.0", "semver": "2", "uid-number": "0.0.5"}}, "node_modules/npm/node_modules/npmconf/node_modules/config-chain": {"version": "1.1.8", "dev": true, "inBundle": true, "dependencies": {"ini": "1", "proto-list": "~1.2.1"}}, "node_modules/npm/node_modules/npmconf/node_modules/config-chain/node_modules/proto-list": {"version": "1.2.3", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/npmlog": {"version": "0.1.1", "dev": true, "inBundle": true, "license": "BSD", "dependencies": {"ansi": "~0.3.0"}}, "node_modules/npm/node_modules/once": {"version": "1.3.0", "dev": true, "inBundle": true, "license": "BSD"}, "node_modules/npm/node_modules/opener": {"version": "1.3.0", "dev": true, "inBundle": true, "license": "WTFPL", "bin": {"opener": "opener.js"}}, "node_modules/npm/node_modules/osenv": {"version": "0.1.0", "dev": true, "inBundle": true, "license": "BSD"}, "node_modules/npm/node_modules/path-is-inside": {"version": "1.0.1", "dev": true, "inBundle": true, "license": "WTFPL"}, "node_modules/npm/node_modules/read": {"version": "1.0.5", "dev": true, "inBundle": true, "license": "BSD", "dependencies": {"mute-stream": "~0.0.4"}, "engines": {"node": ">=0.8"}}, "node_modules/npm/node_modules/read-installed": {"version": "2.0.5", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"read-package-json": "1", "semver": "2", "slide": "~1.1.3", "util-extend": "^1.0.1"}, "optionalDependencies": {"graceful-fs": "2 || 3"}}, "node_modules/npm/node_modules/read-installed/node_modules/util-extend": {"version": "1.0.1", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/read-package-json": {"version": "1.2.2", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"glob": "^4.0.2", "lru-cache": "2", "normalize-package-data": "^0.3.0"}, "optionalDependencies": {"graceful-fs": "2 || 3"}}, "node_modules/npm/node_modules/read-package-json/node_modules/normalize-package-data": {"version": "0.3.0", "dev": true, "inBundle": true, "dependencies": {"github-url-from-git": "~1.1.1", "github-url-from-username-repo": "^0.2.0", "semver": "2"}}, "node_modules/npm/node_modules/read/node_modules/mute-stream": {"version": "0.0.4", "dev": true, "inBundle": true, "license": "BSD"}, "node_modules/npm/node_modules/request": {"version": "2.30.0", "dev": true, "engines": ["node >= 0.8.0"], "inBundle": true, "dependencies": {"forever-agent": "~0.5.0", "json-stringify-safe": "~5.0.0", "mime": "~1.2.9", "node-uuid": "~1.4.0", "qs": "~0.6.0"}, "optionalDependencies": {"aws-sign2": "~0.5.0", "form-data": "~0.1.0", "hawk": "~1.0.0", "http-signature": "~0.10.0", "oauth-sign": "~0.3.0", "tough-cookie": "~0.9.15", "tunnel-agent": "~0.3.0"}}, "node_modules/npm/node_modules/request/node_modules/aws-sign2": {"version": "0.5.0", "dev": true, "inBundle": true, "optional": true, "engines": {"node": "*"}}, "node_modules/npm/node_modules/request/node_modules/forever-agent": {"version": "0.5.0", "dev": true, "inBundle": true, "engines": {"node": "*"}}, "node_modules/npm/node_modules/request/node_modules/form-data": {"version": "0.1.2", "dev": true, "inBundle": true, "optional": true, "dependencies": {"async": "~0.2.9", "combined-stream": "~0.0.4", "mime": "~1.2.11"}, "engines": {"node": ">= 0.6"}}, "node_modules/npm/node_modules/request/node_modules/form-data/node_modules/async": {"version": "0.2.9", "dev": true, "inBundle": true, "optional": true}, "node_modules/npm/node_modules/request/node_modules/form-data/node_modules/combined-stream": {"version": "0.0.4", "dev": true, "inBundle": true, "optional": true, "dependencies": {"delayed-stream": "0.0.5"}, "engines": {"node": "*"}}, "node_modules/npm/node_modules/request/node_modules/form-data/node_modules/combined-stream/node_modules/delayed-stream": {"version": "0.0.5", "dev": true, "inBundle": true, "optional": true, "engines": {"node": ">=0.4.0"}}, "node_modules/npm/node_modules/request/node_modules/hawk": {"version": "1.0.0", "dev": true, "inBundle": true, "optional": true, "dependencies": {"boom": "0.4.x", "cryptiles": "0.2.x", "hoek": "0.9.x", "sntp": "0.2.x"}, "engines": {"node": ">=0.8.0"}}, "node_modules/npm/node_modules/request/node_modules/hawk/node_modules/boom": {"version": "0.4.2", "dev": true, "inBundle": true, "optional": true, "dependencies": {"hoek": "0.9.x"}, "engines": {"node": ">=0.8.0"}}, "node_modules/npm/node_modules/request/node_modules/hawk/node_modules/cryptiles": {"version": "0.2.2", "dev": true, "inBundle": true, "optional": true, "dependencies": {"boom": "0.4.x"}, "engines": {"node": ">=0.8.0"}}, "node_modules/npm/node_modules/request/node_modules/hawk/node_modules/hoek": {"version": "0.9.1", "dev": true, "inBundle": true, "optional": true, "engines": {"node": ">=0.8.0"}}, "node_modules/npm/node_modules/request/node_modules/hawk/node_modules/sntp": {"version": "0.2.4", "dev": true, "inBundle": true, "optional": true, "dependencies": {"hoek": "0.9.x"}, "engines": {"node": ">=0.8.0"}}, "node_modules/npm/node_modules/request/node_modules/http-signature": {"version": "0.10.0", "dev": true, "inBundle": true, "optional": true, "dependencies": {"asn1": "0.1.11", "assert-plus": "0.1.2", "ctype": "0.5.2"}, "engines": {"node": ">=0.8"}}, "node_modules/npm/node_modules/request/node_modules/http-signature/node_modules/asn1": {"version": "0.1.11", "dev": true, "inBundle": true, "optional": true, "engines": {"node": ">=0.4.9"}}, "node_modules/npm/node_modules/request/node_modules/http-signature/node_modules/assert-plus": {"version": "0.1.2", "dev": true, "inBundle": true, "optional": true, "engines": {"node": ">=0.6"}}, "node_modules/npm/node_modules/request/node_modules/http-signature/node_modules/ctype": {"version": "0.5.2", "dev": true, "inBundle": true, "optional": true, "engines": {"node": ">= 0.4"}}, "node_modules/npm/node_modules/request/node_modules/json-stringify-safe": {"version": "5.0.0", "dev": true, "inBundle": true, "license": "BSD"}, "node_modules/npm/node_modules/request/node_modules/mime": {"version": "1.2.11", "dev": true, "inBundle": true}, "node_modules/npm/node_modules/request/node_modules/node-uuid": {"version": "1.4.1", "dev": true, "inBundle": true}, "node_modules/npm/node_modules/request/node_modules/oauth-sign": {"version": "0.3.0", "dev": true, "inBundle": true, "optional": true, "engines": {"node": "*"}}, "node_modules/npm/node_modules/request/node_modules/qs": {"version": "0.6.6", "dev": true, "inBundle": true, "engines": {"node": "*"}}, "node_modules/npm/node_modules/request/node_modules/tough-cookie": {"version": "0.9.15", "dev": true, "inBundle": true, "optional": true, "dependencies": {"punycode": ">=0.2.0"}, "engines": {"node": ">=0.4.12"}}, "node_modules/npm/node_modules/request/node_modules/tough-cookie/node_modules/punycode": {"version": "1.2.3", "dev": true, "engines": ["node", "rhino"], "inBundle": true, "optional": true}, "node_modules/npm/node_modules/request/node_modules/tunnel-agent": {"version": "0.3.0", "dev": true, "inBundle": true, "optional": true, "engines": {"node": "*"}}, "node_modules/npm/node_modules/retry": {"version": "0.6.0", "dev": true, "inBundle": true, "engines": {"node": "*"}}, "node_modules/npm/node_modules/rimraf": {"version": "2.2.8", "dev": true, "inBundle": true, "license": "MIT", "bin": {"rimraf": "bin.js"}}, "node_modules/npm/node_modules/semver": {"version": "2.3.0", "dev": true, "inBundle": true, "license": "BSD", "bin": {"semver": "bin/semver"}}, "node_modules/npm/node_modules/sha": {"version": "1.2.4", "dev": true, "inBundle": true, "license": "BSD", "optionalDependencies": {"graceful-fs": "2 || 3", "readable-stream": "1.0"}}, "node_modules/npm/node_modules/sha/node_modules/readable-stream": {"version": "1.0.27-1", "dev": true, "inBundle": true, "license": "MIT", "optional": true, "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "node_modules/npm/node_modules/sha/node_modules/readable-stream/node_modules/core-util-is": {"version": "1.0.1", "dev": true, "inBundle": true, "license": "MIT", "optional": true}, "node_modules/npm/node_modules/sha/node_modules/readable-stream/node_modules/isarray": {"version": "0.0.1", "dev": true, "inBundle": true, "license": "MIT", "optional": true}, "node_modules/npm/node_modules/sha/node_modules/readable-stream/node_modules/string_decoder": {"version": "0.10.25-1", "dev": true, "inBundle": true, "license": "MIT", "optional": true}, "node_modules/npm/node_modules/slide": {"version": "1.1.5", "dev": true, "inBundle": true, "license": "ISC", "engines": {"node": "*"}}, "node_modules/npm/node_modules/sorted-object": {"version": "1.0.0", "dev": true, "inBundle": true, "license": "WTFPL"}, "node_modules/npm/node_modules/tar": {"version": "0.1.20", "dev": true, "inBundle": true, "license": "BSD", "dependencies": {"block-stream": "*", "fstream": "~0.1.28", "inherits": "2"}}, "node_modules/npm/node_modules/text-table": {"version": "0.2.0", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/uid-number": {"version": "0.0.5", "dev": true, "inBundle": true, "license": "ISC", "engines": {"node": "*"}}, "node_modules/npm/node_modules/which": {"version": "1.0.5", "dev": true, "inBundle": true, "bin": {"which": "bin/which"}, "engines": {"node": "*"}}, "node_modules/oauth-sign": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/oauth-sign/-/oauth-sign-0.3.0.tgz", "integrity": "sha512-Tr31Sh5FnK9YKm7xTUPyDMsNOvMqkVDND0zvK/Wgj7/H9q8mpye0qG2nVzrnsvLhcsX5DtqXD0la0ks6rkPCGQ==", "dev": true, "engines": {"node": "*"}}, "node_modules/object-assign": {"version": "4.1.1", "resolved": "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz", "integrity": "sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/object-keys": {"version": "0.4.0", "resolved": "https://registry.npmjs.org/object-keys/-/object-keys-0.4.0.tgz", "integrity": "sha512-ncrLw+X55z7bkl5PnUvHwFK9FcGuFYo9gtjws2XtSzL+aZ8tm830P60WJ0dSmFVaSalWieW5MD7kEdnXda9yJw==", "dev": true}, "node_modules/on-headers": {"version": "0.0.0", "resolved": "https://registry.npmjs.org/on-headers/-/on-headers-0.0.0.tgz", "integrity": "sha512-sd6W+EIQTNDbMndkGZqf1q6x3PlMxAIoufoNhcfpvzrXhtN+IWVyM2sjdsZ3p+TVddtTG5u0lujTglZ+R1VGvQ==", "dev": true, "engines": {"node": ">= 0.8.0"}}, "node_modules/once": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/once/-/once-1.4.0.tgz", "integrity": "sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==", "dev": true, "dependencies": {"wrappy": "1"}}, "node_modules/open": {"version": "0.0.5", "resolved": "https://registry.npmjs.org/open/-/open-0.0.5.tgz", "integrity": "sha512-+X/dJYLapVO1VbC620DhtNZK9U4/kQVaTQp/Gh7cb6UTLYfGZzzU2ZXkWrOA/wBrf4UqAFwtLqXYTxe4tSnWQQ==", "dev": true, "engines": {"node": ">= 0.6.0"}}, "node_modules/optimist": {"version": "0.3.7", "resolved": "https://registry.npmjs.org/optimist/-/optimist-0.3.7.tgz", "integrity": "sha512-TCx0dXQzVtSCg2OgY/bO9hjM9cV4XYx09TVK+s3+FhkjT6LovsLe+pPMzpWf+6yXK/hUizs2gUoTw3jHM0VaTQ==", "dev": true, "dependencies": {"wordwrap": "~0.0.2"}}, "node_modules/optimist/node_modules/wordwrap": {"version": "0.0.3", "resolved": "https://registry.npmjs.org/wordwrap/-/wordwrap-0.0.3.tgz", "integrity": "sha512-1tMA907+V4QmxV7dbRvb4/8MaRALK6q9Abid3ndMYnbyo8piisCmeONVqVSXqQA3KaP4SLt5b7ud6E2sqP8TFw==", "dev": true, "engines": {"node": ">=0.4.0"}}, "node_modules/os-tmpdir": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/os-tmpdir/-/os-tmpdir-1.0.2.tgz", "integrity": "sha512-D2FR03Vir7FIu45XBY20mTb+/ZSWB00sjU9jdQXt83gDrI4Ztz5Fs7/yy74g2N5SVQY4xY1qDr4rNddwYRVX0g==", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/package": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/package/-/package-1.0.1.tgz", "integrity": "sha512-g6xZR6CO7okjie83sIRJodgGvaXqymfE5GLhN8N2TmZGShmHc/V23hO/vWbdnuy3D81As3pfovw72gGi42l9qA==", "dev": true, "engines": {"node": ">= 0.6.0"}}, "node_modules/pako": {"version": "0.2.9", "resolved": "https://registry.npmjs.org/pako/-/pako-0.2.9.tgz", "integrity": "sha512-NUcwaKxUxWrZLpDG+z/xZaCgQITkA/Dv4V/T6bw7VON6l1Xz/VnrBqrYjZQ12TamKHzITTfOEIYUj48y2KXImA==", "dev": true}, "node_modules/parserlib": {"version": "0.2.5", "resolved": "https://registry.npmjs.org/parserlib/-/parserlib-0.2.5.tgz", "integrity": "sha512-SNu7MNq2Lp5aHXM1HZLyXEHpSAVpHU1y3pvPpxnq6jVK/5WIpKv9aA11PyMeiW9Y+EORem2J7XhiEIaOKizUHA==", "dev": true}, "node_modules/parseurl": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/parseurl/-/parseurl-1.0.1.tgz", "integrity": "sha512-6W9+0+9Ihayqwjgp4OaLLqZ3KDtqPY2PtUPz8YNiy4PamjJv+7x6J9GO93O9rUZOLgaanTPxsKTasxqKkO1iSw==", "dev": true}, "node_modules/path-is-absolute": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz", "integrity": "sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/pause": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/pause/-/pause-0.0.1.tgz", "integrity": "sha512-KG8UEiEVkR3wGEb4m5yZkVCzigAD+cVEJck2CzYZO37ZGJfctvVptVO192MwrtPhzONn6go8ylnOdMhKqi4nfg==", "dev": true}, "node_modules/pend": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/pend/-/pend-1.2.0.tgz", "integrity": "sha512-F3asv42UuXchdzt+xXqfW1OGlVBe+mxa2mqI0pg5yAHZPvFmY3Y6drSf/GQ1A86WgWEN9Kzh/WrgKa6iGcHXLg==", "dev": true}, "node_modules/phantomjs": {"version": "1.9.20", "resolved": "https://registry.npmjs.org/phantomjs/-/phantomjs-1.9.20.tgz", "integrity": "sha512-uja26qe+aIP4ptuCCAk0HNTJXoFIf+7l6RZ9OwCH0lbHpQtcS9VhEeirSqytsarg78wA+Zb5rKwiJK8KezrIcA==", "dev": true, "hasInstallScript": true, "dependencies": {"extract-zip": "~1.5.0", "fs-extra": "~0.26.4", "hasha": "^2.2.0", "kew": "~0.7.0", "progress": "~1.1.8", "request": "~2.67.0", "request-progress": "~2.0.1", "which": "~1.2.2"}, "bin": {"phantomjs": "bin/phantomjs"}}, "node_modules/phantomjs/node_modules/assert-plus": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/assert-plus/-/assert-plus-0.2.0.tgz", "integrity": "sha512-u1L0ZLywRziOVjUhRxI0Qg9G+4RnFB9H/Rq40YWn0dieDgO7vAYeJz6jKAO6t/aruzlDFLAPkQTT87e+f8Imaw==", "dev": true, "engines": {"node": ">=0.8"}}, "node_modules/phantomjs/node_modules/async": {"version": "2.6.4", "resolved": "https://registry.npmjs.org/async/-/async-2.6.4.tgz", "integrity": "sha512-mzo5dfJYwAn29PeiJ0zvwTo04zj8HDJj0Mn8TD7sno7q12prdbnasKJHhkm2c1LgrhlJ0teaea8860oxi51mGA==", "dev": true, "dependencies": {"lodash": "^4.17.14"}}, "node_modules/phantomjs/node_modules/aws-sign2": {"version": "0.6.0", "resolved": "https://registry.npmjs.org/aws-sign2/-/aws-sign2-0.6.0.tgz", "integrity": "sha512-JnJpAS0p9RmixkOvW2XwDxxzs1bd4/VAGIl6Q0EC5YOo+p+hqIhtDhn/nmFnB/xUNXbLkpE2mOjgVIBRKD4xYw==", "dev": true, "engines": {"node": "*"}}, "node_modules/phantomjs/node_modules/boom": {"version": "2.10.1", "resolved": "https://registry.npmjs.org/boom/-/boom-2.10.1.tgz", "integrity": "sha512-KbiZEa9/vofNcVJXGwdWWn25reQ3V3dHBWbS07FTF3/TOehLnm9GEhJV4T6ZvGPkShRpmUqYwnaCrkj0mRnP6Q==", "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial).", "dev": true, "dependencies": {"hoek": "2.x.x"}, "engines": {"node": ">=0.10.40"}}, "node_modules/phantomjs/node_modules/combined-stream": {"version": "1.0.8", "resolved": "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.8.tgz", "integrity": "sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==", "dev": true, "dependencies": {"delayed-stream": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/phantomjs/node_modules/cryptiles": {"version": "2.0.5", "resolved": "https://registry.npmjs.org/cryptiles/-/cryptiles-2.0.5.tgz", "integrity": "sha512-FFN5KwpvvQTTS5hWPxrU8/QE4kQUc6uwZcrnlMBN82t1MgAtq8mnoDwINBly9Tdr02seeIIhtdF+UH1feBYGog==", "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial).", "dev": true, "dependencies": {"boom": "2.x.x"}, "engines": {"node": ">=0.10.40"}}, "node_modules/phantomjs/node_modules/delayed-stream": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz", "integrity": "sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==", "dev": true, "engines": {"node": ">=0.4.0"}}, "node_modules/phantomjs/node_modules/forever-agent": {"version": "0.6.1", "resolved": "https://registry.npmjs.org/forever-agent/-/forever-agent-0.6.1.tgz", "integrity": "sha512-j0K<PERSON><PERSON>hm6zeac4lz3oJ3o65qvgQCcPubiyotZrXqEaG4hNagNYO8qdlUrX5vwqv9ohqeT/Z3j6+yW067yWWdUw==", "dev": true, "engines": {"node": "*"}}, "node_modules/phantomjs/node_modules/form-data": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/form-data/-/form-data-1.0.1.tgz", "integrity": "sha512-M4Yhq2mLogpCtpUmfopFlTTuIe6mSCTgKvnlMhDj3NcgVhA1uS20jT0n+xunKPzpmL5w2erSVtp+SKiJf1TlWg==", "dev": true, "dependencies": {"async": "^2.0.1", "combined-stream": "^1.0.5", "mime-types": "^2.1.11"}, "engines": {"node": ">= 0.10"}}, "node_modules/phantomjs/node_modules/fs-extra": {"version": "0.26.7", "resolved": "https://registry.npmjs.org/fs-extra/-/fs-extra-0.26.7.tgz", "integrity": "sha512-waKu+1KumRhYv8D8gMRCKJGAMI9pRnPuEb1mvgYD0f7wBscg+h6bW4FDTmEZhB9VKxvoTtxW+Y7bnIlB7zja6Q==", "dev": true, "dependencies": {"graceful-fs": "^4.1.2", "jsonfile": "^2.1.0", "klaw": "^1.0.0", "path-is-absolute": "^1.0.0", "rimraf": "^2.2.8"}}, "node_modules/phantomjs/node_modules/graceful-fs": {"version": "4.2.11", "resolved": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz", "integrity": "sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==", "dev": true}, "node_modules/phantomjs/node_modules/hawk": {"version": "3.1.3", "resolved": "https://registry.npmjs.org/hawk/-/hawk-3.1.3.tgz", "integrity": "sha512-X8xbmTc1cbPXcQV4WkLcRMALuyoxhfpFATmyuCxJPOAvrDS4DNnsTAOmKUxMTOWU6TzrTOkxPKwIx5ZOpJVSrg==", "deprecated": "This module moved to @hapi/hawk. Please make sure to switch over as this distribution is no longer supported and may contain bugs and critical security issues.", "dev": true, "dependencies": {"boom": "2.x.x", "cryptiles": "2.x.x", "hoek": "2.x.x", "sntp": "1.x.x"}, "engines": {"node": ">=0.10.32"}}, "node_modules/phantomjs/node_modules/hoek": {"version": "2.16.3", "resolved": "https://registry.npmjs.org/hoek/-/hoek-2.16.3.tgz", "integrity": "sha512-V6Yw1rIcYV/4JsnggjBU0l4Kr+EXhpwqXRusENU1Xx6ro00IHPHYNynCuBTOZAPlr3AAmLvchH9I7N/VUdvOwQ==", "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial).", "dev": true, "engines": {"node": ">=0.10.40"}}, "node_modules/phantomjs/node_modules/http-signature": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/http-signature/-/http-signature-1.1.1.tgz", "integrity": "sha512-iUn0NcRULlDGtqNLN1Jxmzayk8ogm7NToldASyZBpM2qggbphjXzNOiw3piN8tgz+e/DRs6X5gAzFwTI6BCRcg==", "dev": true, "dependencies": {"assert-plus": "^0.2.0", "jsprim": "^1.2.2", "sshpk": "^1.7.0"}, "engines": {"node": ">=0.8", "npm": ">=1.3.7"}}, "node_modules/phantomjs/node_modules/jsonfile": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/jsonfile/-/jsonfile-2.4.0.tgz", "integrity": "sha512-PKllAqbgLgxHaj8TElYymKCAgrASebJrWpTnEkOaTowt23VKXXN0sUeriJ+eh7y6ufb/CC5ap11pz71/cM0hUw==", "dev": true, "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/phantomjs/node_modules/lodash": {"version": "4.17.21", "resolved": "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz", "integrity": "sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==", "dev": true}, "node_modules/phantomjs/node_modules/mime-types": {"version": "2.1.35", "resolved": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz", "integrity": "sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==", "dev": true, "dependencies": {"mime-db": "1.52.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/phantomjs/node_modules/oauth-sign": {"version": "0.8.2", "resolved": "https://registry.npmjs.org/oauth-sign/-/oauth-sign-0.8.2.tgz", "integrity": "sha512-VlF07iu3VV3+BTXj43Nmp6Irt/G7j/NgEctUS6IweH1RGhURjjCc2NWtzXFPXXWWfc7hgbXQdtiQu2LGp6MxUg==", "dev": true, "engines": {"node": "*"}}, "node_modules/phantomjs/node_modules/qs": {"version": "5.2.1", "resolved": "https://registry.npmjs.org/qs/-/qs-5.2.1.tgz", "integrity": "sha512-sh/hmLUTLEiYFhSbRvkM4zj6fMWnbqQt9wrppR2LJA/U/u4xS2eWN8LBE1xc79ExYZJBVZYSMBv/INC7wpE+fw==", "dev": true, "engines": ">=0.10.40"}, "node_modules/phantomjs/node_modules/request": {"version": "2.67.0", "resolved": "https://registry.npmjs.org/request/-/request-2.67.0.tgz", "integrity": "sha512-fzMRDWVEdMktE3foqvL4CBmC+AR8WvcP8pIPx6JSqqhWuPr+BxX9tKx4XiijfyeKtqqRMNpHDWqFMw4JlRPIJg==", "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "dev": true, "dependencies": {"aws-sign2": "~0.6.0", "bl": "~1.0.0", "caseless": "~0.11.0", "combined-stream": "~1.0.5", "extend": "~3.0.0", "forever-agent": "~0.6.1", "form-data": "~1.0.0-rc3", "har-validator": "~2.0.2", "hawk": "~3.1.0", "http-signature": "~1.1.0", "is-typedarray": "~1.0.0", "isstream": "~0.1.2", "json-stringify-safe": "~5.0.1", "mime-types": "~2.1.7", "node-uuid": "~1.4.7", "oauth-sign": "~0.8.0", "qs": "~5.2.0", "stringstream": "~0.0.4", "tough-cookie": "~2.2.0", "tunnel-agent": "~0.4.1"}, "engines": {"node": ">=0.8.0"}}, "node_modules/phantomjs/node_modules/sntp": {"version": "1.0.9", "resolved": "https://registry.npmjs.org/sntp/-/sntp-1.0.9.tgz", "integrity": "sha512-7bgVOAnPj3XjrKY577S+puCKGCRlUrcrEdsMeRXlg9Ghf5df/xNi6sONUa43WrHUd3TjJBF7O04jYoiY0FVa0A==", "deprecated": "This module moved to @hapi/sntp. Please make sure to switch over as this distribution is no longer supported and may contain bugs and critical security issues.", "dev": true, "dependencies": {"hoek": "2.x.x"}, "engines": {"node": ">=0.8.0"}}, "node_modules/phantomjs/node_modules/tough-cookie": {"version": "2.2.2", "resolved": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-2.2.2.tgz", "integrity": "sha512-Knz9Yr0hlBoWQgUKzOIvRg5adinizAf49i2gHRhj6cLjlM304zRw7uyiY22ADniDxnPHXfIeyQD0EAkgpIz0ow==", "deprecated": "ReDoS vulnerability parsing Set-<PERSON><PERSON> https://nodesecurity.io/advisories/130", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/phantomjs/node_modules/tunnel-agent": {"version": "0.4.3", "resolved": "https://registry.npmjs.org/tunnel-agent/-/tunnel-agent-0.4.3.tgz", "integrity": "sha512-e0IoVDWx8SDHc/hwFTqJDQ7CCDTEeGhmcT9jkWJjoGQSpgBz20nAMr80E3Tpk7PatJ1b37DQDgJR3CNSzcMOZQ==", "dev": true, "engines": {"node": "*"}}, "node_modules/phantomjs/node_modules/which": {"version": "1.2.14", "resolved": "https://registry.npmjs.org/which/-/which-1.2.14.tgz", "integrity": "sha512-16uPglFkRPzgiUXYMi1Jf8Z5EzN1iB4V0ZtMXcHZnwsBtQhhHeCqoWw7tsUY42hJGNDWtUsVLTjakIa5BgAxCw==", "dev": true, "dependencies": {"isexe": "^2.0.0"}, "bin": {"which": "bin/which"}}, "node_modules/pinkie": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/pinkie/-/pinkie-2.0.4.tgz", "integrity": "sha512-MnUuEycAemtSaeFSjXKW/aroV7akBbY+Sv+RkyqFjgAe73F+MR0TBWKBRDkmfWq/HiFmdavfZ1G7h4SPZXaCSg==", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/pinkie-promise": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/pinkie-promise/-/pinkie-promise-2.0.1.tgz", "integrity": "sha512-0Gni6D4UcLTbv9c57DfxDGdr41XfgUjqWZu492f0cIGr16zDU06BWP/RAEvOuo7CQ0CNjHaLlM59YJJFm3NWlw==", "dev": true, "dependencies": {"pinkie": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/portscanner": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/portscanner/-/portscanner-0.2.3.tgz", "integrity": "sha512-YEeh34XvoxY9wuoIYRTGj4wqLFA1mVrOUlxiRegNAcRruG4E6Fw6JynpgwIOGYQLzdRptjeuhW2Moa+QzhYJzg==", "dev": true, "dependencies": {"async": "0.1.15"}, "engines": {"node": ">=0.4", "npm": ">=1.0.0"}}, "node_modules/portscanner/node_modules/async": {"version": "0.1.15", "resolved": "https://registry.npmjs.org/async/-/async-0.1.15.tgz", "integrity": "sha512-AGVE6WcRsWX4QudgrVhdDUKAgCv67EwmzP3yEny/AI7/WqM+J8CStwMbGqeXC9p8ih4qota04EaMim/WvA8OCw==", "dev": true, "engines": {"node": "*"}}, "node_modules/postcss": {"version": "0.3.5", "resolved": "https://registry.npmjs.org/postcss/-/postcss-0.3.5.tgz", "integrity": "sha512-TyJE5vZ9RqE8t4OLPET+0IVCucd5yuwKRQZj0oddvIW+7/IjfkUizQkn/zlUgC5xelbzMmspDkrslmwFT/QGMQ==", "dev": true, "dependencies": {"base64-js": "~0.0.6", "source-map": "~0.1.33"}}, "node_modules/pretty-bytes": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/pretty-bytes/-/pretty-bytes-0.1.2.tgz", "integrity": "sha512-TYOKOXttAHbRtQZJGClkQml5sKaM12gGe7xW/SNJkBVs2Jrg1RGTa8a+oM75daQEqKFrFk4MTmvVsgt7uCCc+Q==", "dev": true, "bin": {"pretty-bytes": "cli.js"}, "engines": {"node": ">=0.10.0"}}, "node_modules/pretty-ms": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/pretty-ms/-/pretty-ms-0.1.0.tgz", "integrity": "sha512-Yua6yPqwekHUucqfu33MXazUWDUCcveWVX4mJkigXsA/++3R9IviFHkCbk4mE55a4ab7MVA+/a95rfmyuy1w8A==", "dev": true, "bin": {"pretty-ms": "cli.js"}, "engines": {"node": ">=0.10.0"}}, "node_modules/process-nextick-args": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/process-nextick-args/-/process-nextick-args-2.0.1.tgz", "integrity": "sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag==", "dev": true}, "node_modules/progress": {"version": "1.1.8", "resolved": "https://registry.npmjs.org/progress/-/progress-1.1.8.tgz", "integrity": "sha512-UdA8mJ4weIkUBO224tIarHzuHs4HuYiJvsuGT7j/SPQiUJVjYvNDBIPa0hAorduOfjGohB/qHWRa/lrrWX/mXw==", "dev": true, "engines": {"node": ">=0.4.0"}}, "node_modules/promise": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/promise/-/promise-2.0.0.tgz", "integrity": "sha512-OgMc+sxI3zWF8D5BJGtA0z7/IsrDy1/0cPaDv6HPpqa2fSTo7AdON5U10NbZCUeF+zCAj3PtfPE50Hf02386aA==", "dev": true, "dependencies": {"is-promise": "~1"}}, "node_modules/proxy-agent": {"version": "0.0.2", "resolved": "https://registry.npmjs.org/proxy-agent/-/proxy-agent-0.0.2.tgz", "integrity": "sha512-d/ePObjwS5IIaGIB5LiNXYWQ/STHCn31s/2k93Ks9mPhmbAt6YzHX39ppCN27hOlZhKRbcwtmaB6iJIl28iHAA==", "dev": true, "dependencies": {"http-proxy-agent": "0", "https-proxy-agent": "0", "lru-cache": "~2.3.1", "socks-proxy-agent": "0"}}, "node_modules/proxy-agent/node_modules/lru-cache": {"version": "2.3.1", "resolved": "https://registry.npmjs.org/lru-cache/-/lru-cache-2.3.1.tgz", "integrity": "sha512-EjtmtXFUu+wXm6PW3T6RT1ekQUxobC7B5TDCU0CS0212wzpwKiXs6vLun+JI+OoWmmliWdYqnrpjrlK7W3ELdQ==", "dev": true}, "node_modules/q": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/q/-/q-1.0.1.tgz", "integrity": "sha512-18MnBaCeBX9sLRUdtxz/6onlb7wLzFxCylklyO8n27y5JxJYaGLPu4ccyc5zih58SpEzY8QmfwaWqguqXU6Y+A==", "deprecated": "You or someone you depend on is using Q, the JavaScript Promise library that gave JavaScript developers strong feelings about promises. They can almost certainly migrate to the native JavaScript promise now. Thank you literally everyone for joining me in this bet against the odds. Be excellent to each other.\n\n(For a CapTP with native promises, see @endo/eventual-send and @endo/captp)", "dev": true, "engines": {"node": ">=0.6.0", "teleport": ">=0.2.0"}}, "node_modules/qs": {"version": "0.6.6", "resolved": "https://registry.npmjs.org/qs/-/qs-0.6.6.tgz", "integrity": "sha512-kN+yNdAf29Jgp+AYHUmC7X4QdJPR8czuMWLNLc0aRxkQ7tB3vJQEONKKT9ou/rW7EbqVec11srC9q9BiVbcnHA==", "dev": true, "engines": {"node": "*"}}, "node_modules/rainbowsocks": {"version": "0.1.3", "resolved": "https://registry.npmjs.org/rainbowsocks/-/rainbowsocks-0.1.3.tgz", "integrity": "sha512-/dfeXfgrecw1gCxmgfASMEYxAGLfztnXIwYoXTk4+I2qJY8oLmhKqlYZP7RTeuFPOQHQPCr0U0TbFG/k2fZgjg==", "dev": true, "dependencies": {"debug": "~2.2.0"}}, "node_modules/rainbowsocks/node_modules/debug": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/debug/-/debug-2.2.0.tgz", "integrity": "sha512-X0rGvJcskG1c3TgSCPqHJ0XJgwlcvOC7elJ5Y0hYuKBZoVqWpAMfLOeIh2UI/DCQ5ruodIjvsugZtjUYUw2pUw==", "dev": true, "dependencies": {"ms": "0.7.1"}}, "node_modules/rainbowsocks/node_modules/ms": {"version": "0.7.1", "resolved": "https://registry.npmjs.org/ms/-/ms-0.7.1.tgz", "integrity": "sha512-lRLiIR9fSNpnP6TC4v8+4OU7oStC01esuNowdQ34L+Gk8e5Puoc88IqJ+XAY/B3Mn2ZKis8l8HX90oU8ivzUHg==", "dev": true}, "node_modules/range-parser": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/range-parser/-/range-parser-1.0.3.tgz", "integrity": "sha512-nDsRrtIxVUO5opg/A8T2S3ebULVIfuh8ECbh4w3N4mWxIiT3QILDJDUQayPqm2e8Q8NUa0RSUkGCfe33AfjR3Q==", "dev": true, "engines": {"node": ">= 0.6"}}, "node_modules/raw-body": {"version": "1.1.6", "resolved": "https://registry.npmjs.org/raw-body/-/raw-body-1.1.6.tgz", "integrity": "sha512-XIfWE1upyzaQJ60xmMIY6/pZl7JFBcT59iE4LnXkLOUP4Q7I2SV4waIlH9Oh0VK67qm1IUMZ94V57g2W7V6RpA==", "dev": true, "dependencies": {"bytes": "1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/read-json": {"version": "0.0.0", "resolved": "https://registry.npmjs.org/read-json/-/read-json-0.0.0.tgz", "integrity": "sha512-sU8lz8GMizrQeXN5DGknPeaubZ7QZGNWvIfdJc4a2GIN7c/QplbQ5pNmDfojGjy19um47I2/VjM9TD/nXYTj0g==", "dev": true}, "node_modules/readable-stream": {"version": "1.1.14", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.1.14.tgz", "integrity": "sha512-+MeVjFf4L44XUkhM1eYbD8fyEsxcV81pqMSR5gblfcLCHfZvbrqy4/qYHE+/R5HoBUT11WV5O08Cr1n3YXkWVQ==", "dev": true, "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "node_modules/readdirp": {"version": "0.2.5", "resolved": "https://registry.npmjs.org/readdirp/-/readdirp-0.2.5.tgz", "integrity": "sha512-j3/RLxRiCZSei/4UaYaOAqHa+rQ8ZL9vpolGO9E7mLXiVTb7Fu99eTG74ZmaB/4gCGgy7Veq+U6vw8y7sKJiTw==", "dev": true, "dependencies": {"minimatch": ">=0.2.4"}, "engines": {"node": ">=0.4"}}, "node_modules/redeyed": {"version": "0.5.0", "resolved": "https://registry.npmjs.org/redeyed/-/redeyed-0.5.0.tgz", "integrity": "sha512-AhuOInui6+UEtwpYhp+CqxpP6MD7tnEFTR6yNxEcqbodg+dYDBjDdJJoLsH3k2Wp3mTZkuigP2Bf1bwAd7Fd7Q==", "dev": true, "dependencies": {"esprima-fb": "~12001.1.0-dev-harmony-fb"}}, "node_modules/reduce-component": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/reduce-component/-/reduce-component-1.0.1.tgz", "integrity": "sha512-y0wyCcdQul3hI3xHfIs0vg/jSbboQc/YTOAqaxjFG7At+XSexduuOqBVL9SmOLSwa/ldkbzVzdwuk9s2EKTAZg==", "dev": true}, "node_modules/repeat-string": {"version": "1.6.1", "resolved": "https://registry.npmjs.org/repeat-string/-/repeat-string-1.6.1.tgz", "integrity": "sha512-PV0dzCYDNfRi1jCDbJzpW7jNNDRuCOG/jI5ctQcGKt/clZD+YcPS3yIlWuTJMmESC8aevCFmWJy5wjAFgNqN6w==", "dev": true, "engines": {"node": ">=0.10"}}, "node_modules/replace": {"version": "0.2.10", "resolved": "https://registry.npmjs.org/replace/-/replace-0.2.10.tgz", "integrity": "sha512-CTu46uJCtYGGfDVQL64zqkhN7nD6KRao7e5IaYiPiPwF69/XX4QHhAPV08YWaODrgBiwVbp9Pua436WMp2tdOw==", "dev": true, "dependencies": {"colors": "0.5.x", "minimatch": "~0.2.9", "nomnom": "1.6.x"}, "bin": {"replace": "bin/replace.js", "search": "bin/search.js"}}, "node_modules/replace/node_modules/colors": {"version": "0.5.1", "resolved": "https://registry.npmjs.org/colors/-/colors-0.5.1.tgz", "integrity": "sha512-XjsuUwpDeY98+yz959OlUK6m7mLBM+1MEG5oaenfuQnNnrQk1WvtcvFgN3FNDP3f2NmZ211t0mNEfSEN1h0eIg==", "dev": true, "engines": {"node": ">=0.1.90"}}, "node_modules/replace/node_modules/minimatch": {"version": "0.2.14", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-0.2.14.tgz", "integrity": "sha512-zZ+Jy8lVWlvqqeM8iZB7w7KmQkoJn8djM585z88rywrEbzoqawVa9FR5p2hwD+y74nfuKOjmNvi9gtWJNLqHvA==", "deprecated": "Please update to minimatch 3.0.2 or higher to avoid a RegExp DoS issue", "dev": true, "dependencies": {"lru-cache": "2", "sigmund": "~1.0.0"}, "engines": {"node": "*"}}, "node_modules/request": {"version": "2.34.0", "resolved": "https://registry.npmjs.org/request/-/request-2.34.0.tgz", "integrity": "sha512-mD5mNhfkeaKMg5ZY/hZFbW4lyC/NTn34/ILGQr/XLSuxYOE6vJfL0MTPPXZcZrdt+Nh1Kce+f4B4KbGThIETxQ==", "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "dev": true, "engines": ["node >= 0.8.0"], "dependencies": {"forever-agent": "~0.5.0", "json-stringify-safe": "~5.0.0", "mime": "~1.2.9", "node-uuid": "~1.4.0", "qs": "~0.6.0"}, "optionalDependencies": {"aws-sign2": "~0.5.0", "form-data": "~0.1.0", "hawk": "~1.0.0", "http-signature": "~0.10.0", "oauth-sign": "~0.3.0", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.3.0"}}, "node_modules/request-progress": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/request-progress/-/request-progress-2.0.1.tgz", "integrity": "sha512-dxdraeZVUNEn9AvLrxkgB2k6buTlym71dJk1fk4v8j3Ou3RKNm07BcgbHdj2lLgYGfqX71F+awb1MR+tWPFJzA==", "dev": true, "dependencies": {"throttleit": "^1.0.0"}}, "node_modules/response-time": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/response-time/-/response-time-2.0.0.tgz", "integrity": "sha512-1PeD/WjcPWgv4c1Lpfh+whxgOxauMckWZMWBJNVBXg4Sz/MR1bvtA2V0KOr4gYObkp1GW2NyyiNsJkNMtTOt3w==", "dev": true, "dependencies": {"on-headers": "0.0.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/right-align": {"version": "0.1.3", "resolved": "https://registry.npmjs.org/right-align/-/right-align-0.1.3.tgz", "integrity": "sha512-yqINtL/G7vs2v+dFIZmFUDbnVyFUJFKd6gK22Kgo6R4jfJGFtisKyncWDDULgjfqf4ASQuIQyjJ7XZ+3aWpsAg==", "dev": true, "dependencies": {"align-text": "^0.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/rimraf": {"version": "2.2.8", "resolved": "https://registry.npmjs.org/rimraf/-/rimraf-2.2.8.tgz", "integrity": "sha512-R5KMKHnPAQaZMqLOsyuyUmcIjSeDm+73eoqQpaXA7AZ22BL+6C+1mcUscgOsNd8WVlJuvlgAPsegcx7pjlV0Dg==", "deprecated": "Rimraf versions prior to v4 are no longer supported", "dev": true, "bin": {"rimraf": "bin.js"}}, "node_modules/rndm": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/rndm/-/rndm-1.2.0.tgz", "integrity": "sha512-fJhQQI5tLrQvYIYFpOnFinzv9dwmR7hRnUz1XqP3OJ1jIweTNOd6aTO4jwQSgcBSFUB+/KHJxuGneime+FdzOw==", "dev": true}, "node_modules/run-parallel": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/run-parallel/-/run-parallel-0.3.0.tgz", "integrity": "sha512-fYp5n5QFbxsSmGtq+e//fM3pKw0q+axkoGh3VH7REBMAP2pLpU66VHerQz/nReJbiIjNQVpfDfI5VSO15G43fQ==", "dev": true}, "node_modules/run-series": {"version": "1.1.9", "resolved": "https://registry.npmjs.org/run-series/-/run-series-1.1.9.tgz", "integrity": "sha512-Arc4hUN896vjkqCYrUXquBFtRZdv1PfLbTYP71efP6butxyQ0kWpiNJyAgsxscmQg1cqvHY32/UCBzXedTpU2g==", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}]}, "node_modules/safe-buffer": {"version": "5.1.2", "resolved": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.2.tgz", "integrity": "sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==", "dev": true}, "node_modules/safe-json-parse": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/safe-json-parse/-/safe-json-parse-1.0.1.tgz", "integrity": "sha512-o0JmTu17WGUaUOHa1l0FPGXKBfijbxK6qoHzlkihsDXxzBHvJcA7zgviKR92Xs841rX9pK16unfphLq0/KqX7A==", "dev": true}, "node_modules/safer-buffer": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz", "integrity": "sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==", "dev": true}, "node_modules/sauce-tunnel": {"version": "2.0.6", "resolved": "https://registry.npmjs.org/sauce-tunnel/-/sauce-tunnel-2.0.6.tgz", "integrity": "sha512-q88TAExVaeTT9ZCkRKEGSHok8D6JoD8NSGtCYjiYxrU0q7ICpxKy6tL6WluIXBENDIRnLg7x6qLV6xq2M4LRow==", "dev": true, "dependencies": {"chalk": "~0.4.0", "request": "~2.21.0"}}, "node_modules/sauce-tunnel/node_modules/assert-plus": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/assert-plus/-/assert-plus-0.1.2.tgz", "integrity": "sha512-BbJV8Hq6grYTokkHi/qKS34kfYIFYpu4wKd/H0dARsa6qOqEFH1wboxMwrccAmFjyRjkemjElaVC/sZSUMxHnA==", "dev": true, "engines": {"node": ">=0.6"}}, "node_modules/sauce-tunnel/node_modules/async": {"version": "0.2.10", "resolved": "https://registry.npmjs.org/async/-/async-0.2.10.tgz", "integrity": "sha512-eAkdoKxU6/LkKDBzLpT+t6Ff5EtfSF4wx1WfJiPEEV7WNLnDaRXk0oVysiEPm262roaachGexwUv94WhSgN5TQ==", "dev": true}, "node_modules/sauce-tunnel/node_modules/ctype": {"version": "0.5.2", "resolved": "https://registry.npmjs.org/ctype/-/ctype-0.5.2.tgz", "integrity": "sha512-C+CbWLSk0xdPcp7evo2YEF0o8SLKcDCQsw//accyrf8/NAWYzmUhmL8ZiSokvuwwMQ08RK10U9pkRcyy8EmA5A==", "dev": true, "engines": {"node": ">= 0.4"}}, "node_modules/sauce-tunnel/node_modules/form-data": {"version": "0.0.8", "resolved": "https://registry.npmjs.org/form-data/-/form-data-0.0.8.tgz", "integrity": "sha512-yzpBIhe8Ll+dYTXjd+4ORxbQktke+abD0dJjedvqsVVayMkb+PgLGatJNLwo95Va75l3YDZ01SrouzyW9bC2Fg==", "dev": true, "dependencies": {"async": "~0.2.7", "combined-stream": "~0.0.4", "mime": "~1.2.2"}, "engines": {"node": ">= 0.6"}}, "node_modules/sauce-tunnel/node_modules/hawk": {"version": "0.13.1", "resolved": "https://registry.npmjs.org/hawk/-/hawk-0.13.1.tgz", "integrity": "sha512-f/1H9bruKJfgLN2KFd+666ILQvJYsJcxaCoIdHaaD2zgl7RUa08/202pGJXhOmQ1kTEdMTSxPnbCsu4l6JARhQ==", "deprecated": "This module moved to @hapi/hawk. Please make sure to switch over as this distribution is no longer supported and may contain bugs and critical security issues.", "dev": true, "dependencies": {"boom": "0.4.x", "cryptiles": "0.2.x", "hoek": "0.8.x", "sntp": "0.2.x"}, "engines": {"node": ">=0.8.0"}}, "node_modules/sauce-tunnel/node_modules/hoek": {"version": "0.8.5", "resolved": "https://registry.npmjs.org/hoek/-/hoek-0.8.5.tgz", "integrity": "sha512-NoKdeYUBOlQ7j9dgvT9BEX90rE6HtDkaMFwR6hfOj26LA2Mwyg5026jOpNBhmNrWIGdPnbBK3sQJI3POwh8wqg==", "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial).", "dev": true, "engines": {"node": ">=0.8.0"}}, "node_modules/sauce-tunnel/node_modules/http-signature": {"version": "0.9.11", "resolved": "https://registry.npmjs.org/http-signature/-/http-signature-0.9.11.tgz", "integrity": "sha512-OleF+71prrzyaWDTfTXWvv24N/45SjKCPu/3pzzhj8+MgdGaB7Am3NY0ot5uynrzgTwyQ+yoejuFCncCQxyRSA==", "dev": true, "dependencies": {"asn1": "0.1.11", "assert-plus": "0.1.2", "ctype": "0.5.2"}, "engines": {"node": ">=0.8"}}, "node_modules/sauce-tunnel/node_modules/json-stringify-safe": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/json-stringify-safe/-/json-stringify-safe-4.0.0.tgz", "integrity": "sha512-qzEpz1SDUb9xvA+LDOkNgjekdV7tuC7zDQf14sqMBtujh8kVbQhF11VWm4DeR99yFNjVSjTTfKa40c9ZQOtwXA==", "dev": true}, "node_modules/sauce-tunnel/node_modules/request": {"version": "2.21.0", "resolved": "https://registry.npmjs.org/request/-/request-2.21.0.tgz", "integrity": "sha512-jvDa6FC46ystc0cn+EqtJ4B32SSz/cMX7fEIv0UHX4wsYAKJYXjA5EyAMWpRQ+hWCnX9jPD1b4o7xZ/r1Tyx/Q==", "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "dev": true, "engines": ["node >= 0.8.0"], "dependencies": {"aws-sign": "~0.3.0", "cookie-jar": "~0.3.0", "forever-agent": "~0.5.0", "form-data": "0.0.8", "hawk": "~0.13.0", "http-signature": "~0.9.11", "json-stringify-safe": "~4.0.0", "mime": "~1.2.9", "node-uuid": "~1.4.0", "oauth-sign": "~0.3.0", "qs": "~0.6.0", "tunnel-agent": "~0.3.0"}}, "node_modules/saucelabs": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/saucelabs/-/saucelabs-0.1.1.tgz", "integrity": "sha512-UwTC+y4y6ZCeSMIzYg7R1FgztjkJFGiBAvTfV8Mn2rc8wOEavEA7ZXQneAhQmaR4r55bSFB0wQ/IVemMtnOV3g==", "dev": true, "engines": {"node": "*"}}, "node_modules/scmp": {"version": "0.0.3", "resolved": "https://registry.npmjs.org/scmp/-/scmp-0.0.3.tgz", "integrity": "sha512-ya4sPuUOfcrJnfC+OUqTFgFVBEMOXMS1Xopn0wwIhxKwD4eveTwJoIUN9u1QHJ47nL29/m545dV8KqI92MlHPw==", "deprecated": "scmp v2 uses improved core crypto comparison since Node v6.6.0", "dev": true}, "node_modules/semver": {"version": "1.0.14", "resolved": "https://registry.npmjs.org/semver/-/semver-1.0.14.tgz", "integrity": "sha512-edb8Hl6pnVrKQauQHTqQkRlpZB5RZ/pEe2ir3C3Ztdst0qIayag31dSLsxexLRe80NiWkCffTF5MB7XrGydhSQ==", "dev": true, "bin": {"semver": "bin/semver"}, "engines": {"node": "*"}}, "node_modules/send": {"version": "0.4.3", "resolved": "https://registry.npmjs.org/send/-/send-0.4.3.tgz", "integrity": "sha512-Tl3/iKtlp1WM0hDyackntOVwx5kc8GET/zgEj9AOYRX5ideM/33FeRYk4L19IqioGxCkxHSyq1PThVs6PVvk+w==", "dev": true, "dependencies": {"debug": "1.0.2", "escape-html": "1.0.1", "finished": "1.2.2", "fresh": "0.2.2", "mime": "1.2.11", "range-parser": "~1.0.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/serve-favicon": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/serve-favicon/-/serve-favicon-2.0.1.tgz", "integrity": "sha512-ER7Nk+que+Og6kDJpADjLMkTkllBKWz9FPef5A+uELiYAODTjaMJMszKhzUzsNcvqXM5+mzAdpv/6FaxRlJUng==", "dev": true, "dependencies": {"fresh": "0.2.2"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/serve-index": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/serve-index/-/serve-index-1.1.1.tgz", "integrity": "sha512-HB53eSG+1wd4pjUaXP4TLLL/LmqDlBXvYkjCeWxN3Dv/QSIhhPTD9dgc76kg5Pne5ijoxhJZ0kLmxhnZRIcQtg==", "dev": true, "dependencies": {"accepts": "1.0.3", "batch": "0.5.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/serve-static": {"version": "1.2.3", "resolved": "https://registry.npmjs.org/serve-static/-/serve-static-1.2.3.tgz", "integrity": "sha512-xaOEJYYnhmT2iVnDHcPullns+dFGC18BHseW1ZzkddtPWe4Ot/ZdifPFYk14r+tdWpVNWtXClRRENQ9ODd1Eeg==", "dev": true, "dependencies": {"escape-html": "1.0.1", "parseurl": "1.0.1", "send": "0.4.3"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/shelljs": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/shelljs/-/shelljs-0.3.0.tgz", "integrity": "sha512-Ny0KN4dyT8ZSCE0frtcbAJGoM/HTArpyPkeli1/00aYfm0sbD/Gk/4x7N2DP9QKGpBsiQH7n6rpm1L79RtviEQ==", "dev": true, "bin": {"shjs": "bin/shjs"}, "engines": {"node": ">=0.8.0"}}, "node_modules/sigmund": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/sigmund/-/sigmund-1.0.1.tgz", "integrity": "sha512-fCvEXfh6NWpm+YSuY2bpXb/VIihqWA6hLsgboC+0nl71Q7N7o2eaCW8mJa/NLvQhs6jpd3VZV4UiUQlV6+lc8g==", "dev": true}, "node_modules/sntp": {"version": "0.2.4", "resolved": "https://registry.npmjs.org/sntp/-/sntp-0.2.4.tgz", "integrity": "sha512-bDLrKa/ywz65gCl+LmOiIhteP1bhEsAAzhfMedPoiHP3dyYnAevlaJshdqb9Yu0sRifyP/fRqSt8t+5qGIWlGQ==", "deprecated": "This module moved to @hapi/sntp. Please make sure to switch over as this distribution is no longer supported and may contain bugs and critical security issues.", "dev": true, "dependencies": {"hoek": "0.9.x"}, "engines": {"node": ">=0.8.0"}}, "node_modules/socks-proxy-agent": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/socks-proxy-agent/-/socks-proxy-agent-0.1.2.tgz", "integrity": "sha512-A6HCJpilsagbgSHmNbd6SdW5grzIn+UwJwughjFtSM9FldW44DJcdONeqVU74JNsJca5SDj7ParjW9aLKkNc4g==", "dev": true, "dependencies": {"agent-base": "~1.0.1", "extend": "~1.2.1", "rainbowsocks": "~0.1.2"}}, "node_modules/socks-proxy-agent/node_modules/extend": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/extend/-/extend-1.2.1.tgz", "integrity": "sha512-2/JwIYRpMBDSjbQjUUppNSrmc719crhFaWIdT+TRSVA8gE+6HEobQWqJ6VkPt/H8twS7h/0WWs7veh8wmp98Ng==", "dev": true}, "node_modules/sorted-object": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/sorted-object/-/sorted-object-1.0.0.tgz", "integrity": "sha512-QtH2DSvbUvVTDNNfNt1oIzMWBqsCFG7buFJTK8Ah+35ZvbmKA0ZEigw5pPDfLrL/L2Ds84lD72JknOw7iZq7tw==", "dev": true}, "node_modules/source-map": {"version": "0.1.43", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.1.43.tgz", "integrity": "sha512-VtCvB9SIQhk3aF6h+N85EaqIaBFIAfZ9Cu+NJHHVvc8BbEcnvDcFw6sqQ2dQrT6SlOrZq3tIvyD9+EGq/lJryQ==", "dev": true, "dependencies": {"amdefine": ">=0.0.4"}, "engines": {"node": ">=0.8.0"}}, "node_modules/sshpk": {"version": "1.18.0", "resolved": "https://registry.npmjs.org/sshpk/-/sshpk-1.18.0.tgz", "integrity": "sha512-2p2KJZTSqQ/I3+HX42EpYOa2l3f8Erv8MWKsy2I9uf4wA7yFIkXRffYdsx86y6z4vHtV8u7g+pPlr8/4ouAxsQ==", "dev": true, "dependencies": {"asn1": "~0.2.3", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "getpass": "^0.1.1", "jsbn": "~0.1.0", "safer-buffer": "^2.0.2", "tweetnacl": "~0.14.0"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "engines": {"node": ">=0.10.0"}}, "node_modules/sshpk/node_modules/asn1": {"version": "0.2.6", "resolved": "https://registry.npmjs.org/asn1/-/asn1-0.2.6.tgz", "integrity": "sha512-ix/FxPn0MDjeyJ7i/yoHGFt/EX6LyNbxSEhPPXODPL+KB0VPk86UYfL0lMdy+KCnv+fmvIzySwaK5COwqVbWTQ==", "dev": true, "dependencies": {"safer-buffer": "~2.1.0"}}, "node_modules/sshpk/node_modules/assert-plus": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/assert-plus/-/assert-plus-1.0.0.tgz", "integrity": "sha512-NfJ4UzBCcQGLDlQq7nHxH+tv3kyZ0hHQqF5BO6J7tNJeP5do1llPr8dZ8zHonfhAu0PHAdMkSo+8o0wxg9lZWw==", "dev": true, "engines": {"node": ">=0.8"}}, "node_modules/stream-counter": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/stream-counter/-/stream-counter-0.2.0.tgz", "integrity": "sha512-GjA2zKc2iXUUKRcOxXQmhEx0Ev3XHJ6c8yWGqhQjWwhGrqNwSsvq9YlRLgoGtZ5Kx2Ln94IedaqJ5GUG6aBbxA==", "dev": true, "dependencies": {"readable-stream": "~1.1.8"}, "engines": {"node": ">=0.8.0"}}, "node_modules/string_decoder": {"version": "0.10.31", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-0.10.31.tgz", "integrity": "sha512-ev2QzSzWPYmy9GuqfIVildA4OdcGLeFZQrq5ys6RtiuF+RQQiZWr8TZNyAcuVXyQRYfEO+MsoB/1BuQVhOJuoQ==", "dev": true}, "node_modules/string-template": {"version": "0.1.3", "resolved": "https://registry.npmjs.org/string-template/-/string-template-0.1.3.tgz", "integrity": "sha512-SXksGcdmmihbmiwB+ZcE5xIqTZkVhnYxoOifhK1lQ9zrPNx52r1GBr9BQ4QsWHc4scdaV4hxzZ05gRfkpvDbWw==", "dev": true}, "node_modules/stringstream": {"version": "0.0.6", "resolved": "https://registry.npmjs.org/stringstream/-/stringstream-0.0.6.tgz", "integrity": "sha512-87GEBAkegbBcweToUrdzf3eLhWNg06FJTebl4BVJz/JgWy8CvEr9dRtX5qWphiynMSQlxxi+QqN0z5T32SLlhA==", "dev": true}, "node_modules/strip-ansi": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-0.1.1.tgz", "integrity": "sha512-behete+3uqxecWlDAm5lmskaSaISA+ThQ4oNNBDTBJt0x2ppR6IPqfZNuj6BLaLJ/Sji4TPZlcRyOis8wXQTLg==", "dev": true, "bin": {"strip-ansi": "cli.js"}, "engines": {"node": ">=0.8.0"}}, "node_modules/strip-json-comments": {"version": "0.1.3", "resolved": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-0.1.3.tgz", "integrity": "sha512-d2RPtrkLs8TurFFAIhW8IdM0+cOq+QFETWBGKHO+93eZ4Zt4P1CeJB5LHKW4EfEwabEpPL8/UTO3QX94+lqxwQ==", "dev": true, "bin": {"strip-json-comments": "cli.js"}, "engines": {"node": ">=0.8.0"}}, "node_modules/superagent": {"version": "0.15.7", "resolved": "https://registry.npmjs.org/superagent/-/superagent-0.15.7.tgz", "integrity": "sha512-etQB4NmXUYWg07EiQTLPd2P+8Ti6g3e0g60A6l26SI99bFi/HERvSIPbJCNSf8N+V1a6YqYakj7JoFgLVwax/A==", "deprecated": "Please upgrade to v9.0.0+ as we have fixed a public vulnerability with formidable dependency. Note that v9.0.0+ requires Node.js v14.18.0+. See https://github.com/ladjs/superagent/pull/1800 for insight. This project is supported and maintained by the team at Forward Email @ https://forwardemail.net", "dev": true, "dependencies": {"cookiejar": "1.3.0", "debug": "~0.7.2", "emitter-component": "1.0.0", "formidable": "1.0.14", "methods": "0.0.1", "mime": "1.2.5", "qs": "0.6.5", "reduce-component": "1.0.1"}, "engines": {"node": "*"}}, "node_modules/superagent-proxy": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/superagent-proxy/-/superagent-proxy-0.2.0.tgz", "integrity": "sha512-5ocHirS0zNchXNcz5aQCOGEc2pF2/8gAyd9Otxp4lFHbXTensmKeUF7ArTcL3la0tvZ9rjx/2uSMnjOaTdktCg==", "dev": true, "dependencies": {"proxy-agent": "~0.0.2"}, "peerDependencies": {"superagent": "~0.15.4"}}, "node_modules/superagent/node_modules/debug": {"version": "0.7.4", "resolved": "https://registry.npmjs.org/debug/-/debug-0.7.4.tgz", "integrity": "sha512-EohAb3+DSHSGx8carOSKJe8G0ayV5/i609OD0J2orCkuyae7SyZSz2aoLmQF2s0Pj5gITDebwPH7GFBlqOUQ1Q==", "dev": true, "engines": {"node": "*"}}, "node_modules/superagent/node_modules/methods": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/methods/-/methods-0.0.1.tgz", "integrity": "sha512-pB8oFfci/xcfUgM6DTxc7lbTKifPPgs3mZUOsEgaH+1TTWpmcmv3sHl+5sUHIj2X2W8aPYa2+nJealRHK+Lo6A==", "dev": true}, "node_modules/superagent/node_modules/mime": {"version": "1.2.5", "resolved": "https://registry.npmjs.org/mime/-/mime-1.2.5.tgz", "integrity": "sha512-QfwTOA+zRHSZXxl9Y7ue5ifKDhU1prnh0dO67Vgcl7Lcx0+79vL9A1ln0qtVur8CFSdYq5Zhnw9DDZQgwDh8Ng==", "dev": true, "engines": {"node": "*"}}, "node_modules/superagent/node_modules/qs": {"version": "0.6.5", "resolved": "https://registry.npmjs.org/qs/-/qs-0.6.5.tgz", "integrity": "sha512-n7wA/f30O3SsOw2BVkGUDzjWMw7kXvQJWKtDdgfq5HJvDoad+Jbc6osN1AQ0Iain5plo9e7Cs5fE+xR+DVkPTw==", "dev": true, "engines": {"node": "*"}}, "node_modules/supports-color": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-0.2.0.tgz", "integrity": "sha512-tdCZ28MnM7k7cJDJc7Eq80A9CsRFAAOZUy41npOZCs++qSjfIy7o5Rh46CBk+Dk5FbKJ33X3Tqg4YrV07N5RaA==", "dev": true, "bin": {"supports-color": "cli.js"}, "engines": {"node": ">=0.10.0"}}, "node_modules/tape": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/tape/-/tape-0.2.2.tgz", "integrity": "sha512-bfyf/0yv2FZVsf80b7oo50Lyi35sfjE7VM6206du7LtpbdQP8rbLhZy/stuS/Dcq4w6jE1Pz2zFrHtfeOKbaUA==", "dev": true, "dependencies": {"deep-equal": "~0.0.0", "defined": "~0.0.0", "jsonify": "~0.0.0"}}, "node_modules/temporary": {"version": "0.0.8", "resolved": "https://registry.npmjs.org/temporary/-/temporary-0.0.8.tgz", "integrity": "sha512-NbWqVhmH2arfC/I7upx4VWYJEhp9SSpqjZwzt4LmCuT/7luiAUSt2L3/h9y/3crPnuIdMxg8GsxL9LvEHckdtw==", "dev": true, "dependencies": {"package": ">= 1.0.0 < 1.2.0"}, "engines": {"node": ">= 0.6.0"}}, "node_modules/text-table": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/text-table/-/text-table-0.2.0.tgz", "integrity": "sha512-N+8UisAXDGk8PFXP4HAzVR9nbfmVJ3zYLAWiTIoqC5v5isinhr+r5uaO8+7r3BMfuNIufIsA7RdpVgacC2cSpw==", "dev": true}, "node_modules/throttleit": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/throttleit/-/throttleit-1.0.1.tgz", "integrity": "sha512-vDZpf9Chs9mAdfY046mcPt8fg5QSZr37hEH4TXYBnDF+izxgrbRGUAAaBvIk/fJm9aOFCGFd1EsNg5AZCbnQCQ==", "dev": true, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/time-grunt": {"version": "0.3.2", "resolved": "https://registry.npmjs.org/time-grunt/-/time-grunt-0.3.2.tgz", "integrity": "sha512-J07GME1z9fnUmAE4mL5+cuynwgKIDeQn7YNL8p+uL6EvNlAT+0TmITOBPHB9cABkTxaUlHF3/VQjiqRLVZOTYw==", "dev": true, "dependencies": {"chalk": "^0.4.0", "date-time": "^0.1.0", "hooker": "^0.2.3", "pretty-ms": "^0.1.0", "text-table": "^0.2.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/tiny-lr-fork": {"version": "0.0.5", "resolved": "https://registry.npmjs.org/tiny-lr-fork/-/tiny-lr-fork-0.0.5.tgz", "integrity": "sha512-vhcvny/8f46rrViKV0BOUjVl4c/rDTztnT1+kT7iyD/Igqb/uyiRsKLxMc2Z8CNvTE1Px1tuNrPM+BOUL+3vlQ==", "dev": true, "dependencies": {"debug": "~0.7.0", "faye-websocket": "~0.4.3", "noptify": "~0.0.3", "qs": "~0.5.2"}, "bin": {"tiny-lr-fork": "bin/tiny-lr"}}, "node_modules/tiny-lr-fork/node_modules/debug": {"version": "0.7.4", "resolved": "https://registry.npmjs.org/debug/-/debug-0.7.4.tgz", "integrity": "sha512-EohAb3+DSHSGx8carOSKJe8G0ayV5/i609OD0J2orCkuyae7SyZSz2aoLmQF2s0Pj5gITDebwPH7GFBlqOUQ1Q==", "dev": true, "engines": {"node": "*"}}, "node_modules/tiny-lr-fork/node_modules/qs": {"version": "0.5.6", "resolved": "https://registry.npmjs.org/qs/-/qs-0.5.6.tgz", "integrity": "sha512-KbOrQrP5Ye+0gmq+hwxoJwAFRwExACWqwxj1IDFFgqOw9Poxy3wwSbafd9ZqP6T6ykMfnxM573kt/a4i9ybatQ==", "dev": true, "engines": {"node": "*"}}, "node_modules/tldts": {"version": "6.1.85", "resolved": "https://registry.npmjs.org/tldts/-/tldts-6.1.85.tgz", "integrity": "sha512-gBdZ1RjCSevRPFix/hpaUWeak2/RNUZB4/8frF1r5uYMHjFptkiT0JXIebWvgI/0ZHXvxaUDDJshiA0j6GdL3w==", "dev": true, "optional": true, "dependencies": {"tldts-core": "^6.1.85"}, "bin": {"tldts": "bin/cli.js"}}, "node_modules/tldts-core": {"version": "6.1.85", "resolved": "https://registry.npmjs.org/tldts-core/-/tldts-core-6.1.85.tgz", "integrity": "sha512-DTjUVvxckL1fIoPSb3KE7ISNtkWSawZdpfxGxwiIrZoO6EbHVDXXUIlIuWympPaeS+BLGyggozX/HTMsRAdsoA==", "dev": true, "optional": true}, "node_modules/tmp": {"version": "0.0.33", "resolved": "https://registry.npmjs.org/tmp/-/tmp-0.0.33.tgz", "integrity": "sha512-jRCJlojKnZ3addtTOjdIqoRuPEKBvNXcGYqzO6zWZX8KfKEpnGY5jfggJQ3EjKuu8D4bJRr0y+cYJFmYbImXGw==", "dev": true, "dependencies": {"os-tmpdir": "~1.0.2"}, "engines": {"node": ">=0.6.0"}}, "node_modules/tough-cookie": {"version": "5.1.2", "resolved": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-5.1.2.tgz", "integrity": "sha512-FVDYdxtnj0G6Qm/DhNPSb8Ju59ULcup3tuJxkFb5K8Bv2pUXILbf0xZWU8PX8Ov19OXljbUyveOFwRMwkXzO+A==", "dev": true, "optional": true, "dependencies": {"tldts": "^6.1.32"}, "engines": {"node": ">=16"}}, "node_modules/transformers": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/transformers/-/transformers-2.1.0.tgz", "integrity": "sha512-zJf5m2EIOngmBbDe2fhTPpCjzM2qkZVqrFJZc2jaln+KBeEaYKhS2QMOIkfVrNUyoOwqgbTwOHATzr3jZRQDyg==", "deprecated": "Deprecated, use jstransformer", "dev": true, "dependencies": {"css": "~1.0.8", "promise": "~2.0", "uglify-js": "~2.2.5"}}, "node_modules/transformers/node_modules/uglify-js": {"version": "2.2.5", "resolved": "https://registry.npmjs.org/uglify-js/-/uglify-js-2.2.5.tgz", "integrity": "sha512-viLk+/8G0zm2aKt1JJAVcz5J/5ytdiNaIsKgrre3yvSUjwVG6ZUujGH7E2TiPigZUwLYCe7eaIUEP2Zka2VJPA==", "dev": true, "dependencies": {"optimist": "~0.3.5", "source-map": "~0.1.7"}, "bin": {"uglifyjs": "bin/uglifyjs"}, "engines": {"node": ">=0.4.0"}}, "node_modules/tunnel-agent": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/tunnel-agent/-/tunnel-agent-0.3.0.tgz", "integrity": "sha512-jlGqHGoKzyyjhwv/c9omAgohntThMcGtw8RV/RDLlkbbc08kni/akVxO62N8HaXMVbVsK1NCnpSK3N2xCt22ww==", "dev": true, "engines": {"node": "*"}}, "node_modules/tweetnacl": {"version": "0.14.5", "resolved": "https://registry.npmjs.org/tweetnacl/-/tweetnacl-0.14.5.tgz", "integrity": "sha512-KXXFFdAbFXY4geFIwoyNK+f5Z1b7swfXABfL7HXCmoIWMKU3dmS26672A4EeQtDzLKy7SXmfBu51JolvEKwtGA==", "dev": true}, "node_modules/type-is": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/type-is/-/type-is-1.2.1.tgz", "integrity": "sha512-6/sfH4bn0JhSRWTHv1dGhkfIyftWIkYPtpiNRM/G5/45RazNmI8WaeE76vBQOZNijVYkmmxqOTJiwBcRMlBbQw==", "dev": true, "dependencies": {"mime-types": "1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/typedarray": {"version": "0.0.6", "resolved": "https://registry.npmjs.org/typedarray/-/typedarray-0.0.6.tgz", "integrity": "sha512-/aCDEGatGvZ2BIk+HmLf4ifCJFwvKFNb9/JeZPMulfgFracn9QFcAf5GO8B/mweUjSoblS5In0cWhqpfs/5PQA==", "dev": true}, "node_modules/uglify-js": {"version": "2.8.29", "resolved": "https://registry.npmjs.org/uglify-js/-/uglify-js-2.8.29.tgz", "integrity": "sha512-qLq/4y2pjcU3vhlhseXGGJ7VbFO4pBANu0kwl8VCa9KEI0V8VfZIx2Fy3w01iSTA/pGwKZSmu/+I4etLNDdt5w==", "dev": true, "dependencies": {"source-map": "~0.5.1", "yargs": "~3.10.0"}, "bin": {"uglifyjs": "bin/uglifyjs"}, "engines": {"node": ">=0.8.0"}, "optionalDependencies": {"uglify-to-browserify": "~1.0.0"}}, "node_modules/uglify-js/node_modules/source-map": {"version": "0.5.7", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.5.7.tgz", "integrity": "sha512-LbrmJOMUSdEVxIKvdcJzQC+nQhe8FUZQTXQy6+I75skNgn3OoQ0DZA8YnFa7gp8tqtL3KPf1kmo0R5DoApeSGQ==", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/uglify-to-browserify": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/uglify-to-browserify/-/uglify-to-browserify-1.0.2.tgz", "integrity": "sha512-vb2s1lYx2xBtUgy+ta+b2J/GLVUR+wmpINwHePmPRhOsIVCG2wDzKJ0n14GslH1BifsqVzSOwQhRaCAsZ/nI4Q==", "dev": true}, "node_modules/uid2": {"version": "0.0.4", "resolved": "https://registry.npmjs.org/uid2/-/uid2-0.0.4.tgz", "integrity": "sha512-IevTus0SbGwQzYh3+fRsAMTVVPOoIVufzacXcHPmdlle1jUpq7BRL+mw3dgeLanvGZdwwbWhRV6XrcFNdBmjWA==", "dev": true}, "node_modules/underscore": {"version": "1.7.0", "resolved": "https://registry.npmjs.org/underscore/-/underscore-1.7.0.tgz", "integrity": "sha512-cp0oQQyZhUM1kpJDLdGO1jPZHgS/MpzoWYfe9+CM2h/QGDZlqwT2T3YGukuBdaNJ/CAPoeyAZRRHz8JFo176vA==", "dev": true}, "node_modules/underscore.string": {"version": "2.2.1", "resolved": "https://registry.npmjs.org/underscore.string/-/underscore.string-2.2.1.tgz", "integrity": "sha512-3FVmhXqelrj6gfgp3Bn6tOavJvW0dNH2T+heTD38JRxIrAbiuzbqjknszoOYj3DyFB1nWiLj208Qt2no/L4cIA==", "dev": true, "engines": {"node": "*"}}, "node_modules/util-deprecate": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz", "integrity": "sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==", "dev": true}, "node_modules/utils-merge": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/utils-merge/-/utils-merge-1.0.0.tgz", "integrity": "sha512-HwU9SLQEtyo+0uoKXd1nkLqigUWLB+QuNQR4OcmB73eWqksM5ovuqcycks2x043W8XVb75rG1HQ0h93TMXkzQQ==", "dev": true, "engines": {"node": ">= 0.4.0"}}, "node_modules/vary": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/vary/-/vary-0.1.0.tgz", "integrity": "sha512-tyyeG46NQdwyVP/RsWLSrT78ouwEuvwk9gK8vQK4jdXmqoXtTXW+vsCfNcnqRhigF8olV34QVZarmAi6wBV2Mw==", "dev": true, "engines": {"node": ">= 0.8.0"}}, "node_modules/verror": {"version": "1.10.0", "resolved": "https://registry.npmjs.org/verror/-/verror-1.10.0.tgz", "integrity": "sha512-ZZKSmDAEFOijERBLkmYfJ+vmk3w+7hOLYDNkRCuRuMJGEmqYNCNLyBBFwWKVMhfwaEF3WOd0Zlw86U/WC/+nYw==", "dev": true, "engines": ["node >=0.6.0"], "dependencies": {"assert-plus": "^1.0.0", "core-util-is": "1.0.2", "extsprintf": "^1.2.0"}}, "node_modules/verror/node_modules/assert-plus": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/assert-plus/-/assert-plus-1.0.0.tgz", "integrity": "sha512-NfJ4UzBCcQGLDlQq7nHxH+tv3kyZ0hHQqF5BO6J7tNJeP5do1llPr8dZ8zHonfhAu0PHAdMkSo+8o0wxg9lZWw==", "dev": true, "engines": {"node": ">=0.8"}}, "node_modules/verror/node_modules/core-util-is": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/core-util-is/-/core-util-is-1.0.2.tgz", "integrity": "sha512-3lqz5YjWTYnW6dlDa5TLaTCcShfar1e40rmcJVwCBJC6mWlFuj0eCHIElmG1g5kyuJ/GD+8Wn4FFCcz4gJPfaQ==", "dev": true}, "node_modules/vhost": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/vhost/-/vhost-1.0.0.tgz", "integrity": "sha512-j5oZxSO2DUNZfdQBZlNrxLAGDg1BEgT7njN73f9XDldKoNzv00zeyhgUY5jR70G6kdTrF0xOzESQnnvaJ/Td4g==", "dev": true}, "node_modules/vow": {"version": "0.4.4", "resolved": "https://registry.npmjs.org/vow/-/vow-0.4.4.tgz", "integrity": "sha512-r2f2JN12F0VSJLb+6HIyyGnbZt4kgxzMf1bgHaa5fQp6Cq2HyQNQ+UOcFH2H3rfzOOSTPM3ebnZ7COaYf4AYnw==", "dev": true, "engines": {"node": ">= 0.4.0"}}, "node_modules/vow-fs": {"version": "0.3.2", "resolved": "https://registry.npmjs.org/vow-fs/-/vow-fs-0.3.2.tgz", "integrity": "sha512-raT20j8SL2sCX3MlLCv6SWw3IeFU+3AuCDjGh5Q8Xs4EoT5GDjftg/a8r0PPr6gV7qyJET0OtPQ1hOvE7imEcw==", "dev": true, "dependencies": {"glob": "3.2.8", "node-uuid": "1.4.0", "vow": "0.4.4", "vow-queue": "0.3.1"}, "engines": {"node": ">= 0.6.0"}}, "node_modules/vow-fs/node_modules/glob": {"version": "3.2.8", "resolved": "https://registry.npmjs.org/glob/-/glob-3.2.8.tgz", "integrity": "sha512-Y3icmja4O+RjRYHMc97ggBZOljMWzBFGEOk96IXbNGRbQEZrz15HAcqe89t9WUcmcDdVVNAK5ar2lTpL+SutNA==", "deprecated": "Glob versions prior to v9 are no longer supported", "dev": true, "dependencies": {"inherits": "2", "minimatch": "~0.2.11"}, "engines": {"node": "*"}}, "node_modules/vow-fs/node_modules/minimatch": {"version": "0.2.14", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-0.2.14.tgz", "integrity": "sha512-zZ+Jy8lVWlvqqeM8iZB7w7KmQkoJn8djM585z88rywrEbzoqawVa9FR5p2hwD+y74nfuKOjmNvi9gtWJNLqHvA==", "deprecated": "Please update to minimatch 3.0.2 or higher to avoid a RegExp DoS issue", "dev": true, "dependencies": {"lru-cache": "2", "sigmund": "~1.0.0"}, "engines": {"node": "*"}}, "node_modules/vow-fs/node_modules/node-uuid": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/node-uuid/-/node-uuid-1.4.0.tgz", "integrity": "sha512-Vns3Mj1WBYNwPchf2T/pt9q2GUpM97JvLekAkAwWYX1H2kIxYQ+jUb3GWmaNRboP5XoS3p3nxptIv00I+cOtLg==", "deprecated": "Use uuid module instead", "dev": true}, "node_modules/vow-queue": {"version": "0.3.1", "resolved": "https://registry.npmjs.org/vow-queue/-/vow-queue-0.3.1.tgz", "integrity": "sha512-rX8acYHR7w/aQohdBkb+ihVY1MZpBlsrDVgNT95+m8V0Mc1YVffn4TjxX3gAQ7nZoWw7LusnaYywSb2gmekpXQ==", "dev": true, "dependencies": {"vow": "~0.4.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/w3cjs": {"version": "0.1.25", "resolved": "https://registry.npmjs.org/w3cjs/-/w3cjs-0.1.25.tgz", "integrity": "sha512-EAhN/Fz8HLEaXM9t3S7VDrTAyGUmyKwhgG82v4C0Mh/TQnHzN+/ODIVIvmjuCUs0ofJYqF4AQrfijYRePnlh7g==", "dev": true, "dependencies": {"commander": "~2.0.0", "superagent": "~0.15.7", "superagent-proxy": "~0.2.0"}, "bin": {"w3cjs": "bin/w3cjs"}, "engines": {"node": "*"}}, "node_modules/w3cjs/node_modules/commander": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/commander/-/commander-2.0.0.tgz", "integrity": "sha512-qebjpyeaA/nJ4w3EO2cV2++/zEkccPnjWogzA2rff+Lk8ILI75vULeTmyd4wPxWdKwtP3J+G39IXVZadh0UHyw==", "dev": true, "engines": {"node": ">= 0.6.x"}}, "node_modules/which": {"version": "1.0.9", "resolved": "https://registry.npmjs.org/which/-/which-1.0.9.tgz", "integrity": "sha512-E87fdQ/eRJr9W1X4wTPejNy9zTW3FI2vpCZSJ/HAY+TkjKVC0TUm1jk6vn2Z7qay0DQy0+RBGdXxj+RmmiGZKQ==", "dev": true, "bin": {"which": "bin/which"}}, "node_modules/window-size": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/window-size/-/window-size-0.1.0.tgz", "integrity": "sha512-1pTPQDKTdd61ozlKGNCjhNRd+KPmgLSGa3mZTHoOliaGcESD8G1PXhh7c1fgiPjVbNVfgy2Faw4BI8/m0cC8Mg==", "dev": true, "engines": {"node": ">= 0.8.0"}}, "node_modules/with": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/with/-/with-3.0.1.tgz", "integrity": "sha512-kkmVGj873jXl71lBDCCoZ35tOOyUsMQv2XFmov8hS39PR5u8aTFyN7f0ZjOJ5QipflBdHGT5d9tpwBtBDrE7Ig==", "dev": true, "dependencies": {"uglify-js": "~2.4.12"}}, "node_modules/with/node_modules/async": {"version": "0.2.10", "resolved": "https://registry.npmjs.org/async/-/async-0.2.10.tgz", "integrity": "sha512-eAkdoKxU6/LkKDBzLpT+t6Ff5EtfSF4wx1WfJiPEEV7WNLnDaRXk0oVysiEPm262roaachGexwUv94WhSgN5TQ==", "dev": true}, "node_modules/with/node_modules/source-map": {"version": "0.1.34", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.1.34.tgz", "integrity": "sha512-yfCwDj0vR9RTwt3pEzglgb3ZgmcXHt6DjG3bjJvzPwTL+5zDQ2MhmSzAcTy0GTiQuCiriSWXvWM1/NhKdXuoQA==", "dev": true, "dependencies": {"amdefine": ">=0.0.4"}, "engines": {"node": ">=0.8.0"}}, "node_modules/with/node_modules/uglify-js": {"version": "2.4.24", "resolved": "https://registry.npmjs.org/uglify-js/-/uglify-js-2.4.24.tgz", "integrity": "sha512-tktIjwackfZLd893KGJmXc1hrRHH1vH9Po3xFh1XBjjeGAnN02xJ3SuoA+n1L29/ZaCA18KzCFlckS+vfPugiA==", "dev": true, "dependencies": {"async": "~0.2.6", "source-map": "0.1.34", "uglify-to-browserify": "~1.0.0", "yargs": "~3.5.4"}, "bin": {"uglifyjs": "bin/uglifyjs"}, "engines": {"node": ">=0.4.0"}}, "node_modules/with/node_modules/wordwrap": {"version": "0.0.2", "resolved": "https://registry.npmjs.org/wordwrap/-/wordwrap-0.0.2.tgz", "integrity": "sha512-xSBsCeh+g+dinoBv3GAOWM4LcVVO68wLXRanibtBSdUvkGWQRGeE9P7IwU9EmDDi4jA6L44lz15CGMwdw9N5+Q==", "dev": true, "engines": {"node": ">=0.4.0"}}, "node_modules/with/node_modules/yargs": {"version": "3.5.4", "resolved": "https://registry.npmjs.org/yargs/-/yargs-3.5.4.tgz", "integrity": "sha512-5j382E4xQSs71p/xZQsU1PtRA2HXPAjX0E0DkoGLxwNASMOKX6A9doV1NrZmj85u2Pjquz402qonBzz/yLPbPA==", "dev": true, "dependencies": {"camelcase": "^1.0.2", "decamelize": "^1.0.0", "window-size": "0.1.0", "wordwrap": "0.0.2"}}, "node_modules/wordwrap": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/wordwrap/-/wordwrap-1.0.0.tgz", "integrity": "sha512-gvVzJFlPycKc5dZN4yPkP8w7Dc37BtP1yczEneOb4uq34pXZcvrtRTmWV8W+Ume+XCxKgbjM+nevkyFPMybd4Q==", "dev": true}, "node_modules/wrappy": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz", "integrity": "sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==", "dev": true}, "node_modules/xmlbuilder": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/xmlbuilder/-/xmlbuilder-2.3.0.tgz", "integrity": "sha512-ChyDxENeigxP8BxqDW52Nvx9v46Ha8HDNYGZtkIQQdtzoE8cgUciBZS9xJlHuvJCSWenIci0jGYicWVpvvkR/A==", "dev": true, "dependencies": {"lodash.assign": "~2.4.1", "lodash.create": "~2.4.1", "lodash.isarray": "~2.4.1", "lodash.isempty": "~2.4.1", "lodash.isfunction": "~2.4.1", "lodash.isobject": "~2.4.1"}, "engines": {"node": "0.8.x || 0.10.x || 0.11.x"}}, "node_modules/xtend": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/xtend/-/xtend-2.1.2.tgz", "integrity": "sha512-vMNKzr2rHP9Dp/e1NQFnLQlwlhp9L/LfvnsVdHxN1f+uggyVI3i08uD14GPvCToPkdsRfyPqIyYGmIk58V98ZQ==", "dev": true, "dependencies": {"object-keys": "~0.4.0"}, "engines": {"node": ">=0.4"}}, "node_modules/yargs": {"version": "3.10.0", "resolved": "https://registry.npmjs.org/yargs/-/yargs-3.10.0.tgz", "integrity": "sha512-QFzUah88GAGy9lyDKGBqZdkYApt63rCXYBGYnEP4xDJPXNqXXnBDACnbrXnViV6jRSqAePwrATi2i8mfYm4L1A==", "dev": true, "dependencies": {"camelcase": "^1.0.2", "cliui": "^2.1.0", "decamelize": "^1.0.0", "window-size": "0.1.0"}}, "node_modules/yauzl": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/yauzl/-/yauzl-2.4.1.tgz", "integrity": "sha512-TXNR2Feu/p/8k5YRy4z45wCUhoncIrZywmRd+xW0IvB3lWTAM7F6wVbeJvRjO0dplQ8oqmJEj/TpJuULBV/hbw==", "dev": true, "dependencies": {"fd-slicer": "~1.0.1"}}, "node_modules/zlib-browserify": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/zlib-browserify/-/zlib-browserify-0.0.1.tgz", "integrity": "sha512-fheIDCKXU0YAGZMv4FFwVTBMQRSv2ZjNqRN1VkZjetZDK/BC/hViEhasTh0kTeogcsIAl5gYE04GN53trT+cFw==", "dev": true}}}