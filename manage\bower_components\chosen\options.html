<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title>Chosen: A jQuery Plugin by Harvest to Tame Unwieldy Select Boxes</title>
  <link rel="stylesheet" href="docsupport/style.css">
  <link rel="stylesheet" href="docsupport/prism.css">
  <link rel="stylesheet" href="chosen.css">
  <style type="text/css" media="all">
    /* fix rtl for demo */
    .chosen-rtl .chosen-drop { left: -9000px; }
  </style>
</head>
<body>
  <div id="container">
    <div id="content">
      <header>
        <h1>Chosen <small>(<span id="latest-version">v1.1.0</span>)</small></h1>
      </header>
      <p><PERSON><PERSON> has a number of options and attributes that allow you to have full control of your select boxes.</p>

      <h2><a name="options" class="anchor" href="#options">Options</a></h2>
      <p>The following options are available to pass into <PERSON><PERSON> on instantiation.</p>

      <h3>Example:</h3>
<pre>
  <code class="language-javascript">$(".my_select_box").chosen({
    disable_search_threshold: 10,
    no_results_text: "Oops, nothing found!",
    width: "95%"
  });</code>
</pre>

      <table class="docs-table">
        <tr>
          <th>Option</th><th>Default</th><th>Description</th>
        </tr>
        <tr>
          <td>allow_single_deselect</td>
          <td>false</td>
          <td>When set to <code class="language-javascript">true</code> on a single select, Chosen adds a UI element which selects the first elment (if it is blank).</td>
        </tr>
        <tr>
          <td>disable_search</td>
          <td>false</td>
          <td>When set to <code class="language-javascript">true</code>, Chosen will not display the search field (single selects only).</td>
        </tr>
        <tr>
          <td>disable_search_threshold</td>
          <td>0</td>
          <td>Hide the search input on single selects if there are fewer than (n) options.</td>
        </tr>
        <tr>
          <td>enable_split_word_search</td>
          <td>true</td>
          <td>By default, searching will match on any word within an option tag. Set this option to <code class="language-javascript">false</code> if you want to only match on the entire text of an option tag.</td>
        </tr>
        <tr>
          <td>inherit_select_classes</td>
          <td>false</td>
          <td>When set to <code class="language-javascript">true</code>, Chosen will grab any classes on the original select field and add them to Chosen’s container div.</td>
        </tr>
        <tr>
          <td>max_selected_options</td>
          <td>Infinity</td>
          <td>Limits how many options the user can select. When the limit is reached, the <code class="language-javascript">chosen:maxselected</code> event is triggered.</td>
        </tr>
        <tr>
          <td>no_results_text</td>
          <td>"No results match"</td>
          <td>The text to be displayed when no matching results are found. The current search is shown at the end of the text (<i>e.g.</i>,
           No results match "Bad Search").</td>
        </tr>
        <tr>
          <td>placeholder_text_multiple</td>
          <td>"Select Some Options"</td>
          <td>The text to be displayed as a placeholder when no options are selected for a multiple select.</td>
        </tr>
        <tr>
          <td>placeholder_text_single</td>
          <td>"Select an Option"</td>
          <td>The text to be displayed as a placeholder when no options are selected for a single select.</td>
        </tr>
        <tr>
          <td>search_contains</td>
          <td>false</td>
          <td>By default, Chosen’s search matches starting at the beginning of a word. Setting this option to <code class="language-javascript">true</code> allows matches starting from anywhere within a word. This is especially useful for options that include a lot of special characters or phrases in ()s and []s.</td>
        </tr>
        <tr>
          <td>single_backstroke_delete</td>
          <td>true</td>
          <td>By default, pressing delete/backspace on multiple selects will remove a selected choice. When <code class="language-javascript">false</code>, pressing delete/backspace will highlight the last choice, and a second press deselects it.</td>
        </tr>
        <tr>
          <td>width</td>
          <td>Original select width.</td>
          <td>The width of the Chosen select box. By default, Chosen attempts to match the width of the select box you are replacing. If your select is hidden when Chosen is instantiated, you must specify a width or the select will show up with a width of 0.</td>
        </tr>
        <tr>
          <td>display_disabled_options</td>
          <td>true</td>
          <td>By default, Chosen includes disabled options in search results with a special styling. Setting this option to false will hide disabled results and exclude them from searches.</td>
        </tr>
        <tr>
          <td>display_selected_options</td>
          <td>true</td>
          <td>
            <p>By default, Chosen includes selected options in search results with a special styling. Setting this option to false will hide selected results and exclude them from searches.</p>
            <p><strong>Note:</strong> this is for multiple selects only. In single selects, the selected result will always be displayed.</p>
          </td>
        </tr>
      </table>

      <h2><a name="attributes" class="anchor" href="#attributes">Attributes</a></h2>
      <p>Certain attributes placed on the select tag or its options can be used to configure Chosen.</p>

      <h3>Example:</h3>

<pre>
  <code class="language-markup">&lt;select class="my_select_box" data-placeholder="Select Your Options"&gt;
    &lt;option value="1"&gt;Option 1&lt;/option&gt;
    &lt;option value="2" selected&gt;Option 2&lt;/option&gt;
    &lt;option value="3" disabled&gt;Option 3&lt;/option&gt;
  &lt;/select&gt;</code>
</pre>

      <table class="docs-table">
        <tr>
          <th>Attribute</th><th>Description</th>
        </tr>
        <tr>
          <td>data-placeholder</td>
          <td>
            <p>The text to be displayed as a placeholder when no options are selected for a select. Defaults to "Select an Option" for single selects or "Select Some Options" for multiple selects.</p>
            <p><strong>Note:</strong>This attribute overrides anything set in the <code class="language-javascript">placeholder_text_multiple</code> or <code class="language-javascript">placeholder_text_single</code> options.</p>
          </td>
        </tr>
        <tr>
          <td>multiple</td>
          <td>The attribute <code class="language-html">multiple</code> on your select box dictates whether Chosen will render a multiple or single select.</td>
        </tr>
        <tr>
          <td>selected, disabled</td>
          <td>Chosen automatically highlights selected options and disables disabled options.</td>
        </tr>
      </table>

      <h2><a name="classes" class="anchor" href="#classes">Classes</a></h2>
      <p>Classes placed on the select tag can be used to configure Chosen.</p>

      <h3>Example:</h3>

<pre>
  <code class="language-markup">&lt;select class="my_select_box chosen-rtl"&gt;
    &lt;option value="1"&gt;Option 1&lt;/option&gt;
    &lt;option value="2"&gt;Option 2&lt;/option&gt;
    &lt;option value="3"&gt;Option 3&lt;/option&gt;
  &lt;/select&gt;</code>
</pre>

      <table class="docs-table">
        <tr>
          <th>Classname</th>
          <th>Description</th>
        </tr>
        <tr>
          <td>chosen-rtl</td>
          <td>
            <p>Chosen supports right-to-left text in select boxes. Add the class <code class="language-html">chosen-rtl</code> to your select tag to support right-to-left text options.</p>
            <p><strong>Note:</strong> The <code class="language-html">chosen-rtl</code> class will pass through to the Chosen select even when the <code class="language-javascript">inherit_select_classes</code> option is set to <code class="language-javascript">false</code>.</p>
          </td>
        </tr>
      </table>

      <h2><a name="triggered-events" class="anchor" href="#triggered-events">Triggered Events</a></h2>
      <p>Chosen triggers a number of standard and custom events on the original select field.</p>

      <h3>Example:</h3>

<pre>
  <code class="language-javascript">$('.my_select_box').on('change', function(evt, params) {
    do_something(evt, params);
  });</code>
</pre>

      <table class="docs-table">
        <tr>
          <th>Event</th><th>Description</th>
        </tr>
        <tr>
          <td>change</td>
          <td>
            <p>Chosen triggers the standard DOM event whenever a selection is made (it also sends a <code class="language-javascript">selected</code> or <code class="language-javascript">deselected</code> parameter that tells you which option was changed).</p>
            <p><strong>Note:</strong> in order to use change in the Prototype version, you have to include the <a href="https://github.com/kangax/protolicious/blob/5b56fdafcd7d7662c9d648534225039b2e78e371/event.simulate.js">Event.simulate</a> class. The selected and deselected parameters are not available for Prototype.</p>
          </td>
        </tr>
        <tr>
          <td>chosen:ready</td>
          <td>Triggered after Chosen has been fully instantiated.</td>
        </tr>
        <tr>
          <td>chosen:maxselected</td>
          <td>Triggered if <code class="language-javascript">max_selected_options</code> is set and that total is broken.</td>
        </tr>
        <tr>
          <td>chosen:showing_dropdown</td>
          <td>Triggered when Chosen’s dropdown is opened.</td>
        </tr>
        <tr>
          <td>chosen:hiding_dropdown</td>
          <td>Triggered when Chosen’s dropdown is closed.</td>
        </tr>
        <tr>
          <td>chosen:no_results</td>
          <td>Triggered when a search returns no matching results.</td>
        </tr>
      </table>

      <p style="margin-top: 1em;">
        <strong>Note:</strong> all custom Chosen events (those that being with <code class="language-javascript">chosen:</code>) also include the <code class="language-javascript">chosen</code> object as a parameter.
      </p>

      <h2><a name="triggerable-events" class="anchor" href="#triggerable-events">Triggerable Events</a></h2>
      <p>You can trigger several events on the original select field to invoke a behavior in Chosen.</p>

      <h3>Example:</h3>

<pre>
  <code class="language-javascript">// tell Chosen that a select has changed
    $('.my_select_box').trigger('chosen:updated');</code>
</pre>

      <table class="docs-table">
        <tr>
          <th>Event</th><th>Description</th>
        </tr>
        <tr>
          <td>chosen:updated</td>
          <td>This event should be triggered whenever Chosen’s underlying select element changes (such as a change in selected options).</td>
        </tr>
        <tr>
          <td>chosen:activate</td>
          <td>This is the equivalant of focusing a standard HTML select field. When activated, Chosen will capure keypress events as if you had clicked the field directly.</td>
        </tr>
        <tr>
          <td>chosen:open</td>
          <td>This event activates Chosen and also displays the search results.</td>
        </tr>
        <tr>
          <td>chosen:close</td>
          <td>This event deactivates Chosen and hides the search results.</td>
        </tr>
      </table>

      <footer>
        &copy; 2011&ndash;2013 <a href="http://www.getharvest.com/">Harvest</a>. Chosen is licensed under the <a href="https://github.com/harvesthq/chosen/blob/master/LICENSE.md">MIT license</a>.
      </footer>

    </div>
  </div>
  <div class="oss-bar">
    <ul>
      <li><a class="fork" href="https://github.com/harvesthq/chosen">Fork on Github</a></li>
      <li><a class="harvest" href="http://www.getharvest.com/">Built by Harvest</a></li>
    </ul>
  </div>
  <script src="docsupport/prism.js" type="text/javascript" charset="utf-8"></script>
</body>
</html>
