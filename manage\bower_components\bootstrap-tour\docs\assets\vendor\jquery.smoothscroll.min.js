/*! http://mths.be/smoothscroll v1.5.2 by @mathias */(function(e,t){var n=function(){var n=t(e.documentElement),r=t(e.body),i;if(n.scrollTop())return n;i=r.scrollTop();return r.scrollTop(i+1).scrollTop()==i?n:r.scrollTop(i)}();t.fn.smoothScroll=function(e){e=~~e||400;return this.find('a[href*="#"]').click(function(r){var i=this.hash,s=t(i);if(location.pathname.replace(/^\//,"")===this.pathname.replace(/^\//,"")&&location.hostname===this.hostname&&s.length){r.preventDefault();n.stop().animate({scrollTop:s.offset().top},e,function(){location.hash=i})}}).end()}})(document,jQuery);