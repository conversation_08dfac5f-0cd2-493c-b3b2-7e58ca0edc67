# coding: utf-8
__author__ = '<PERSON> (ZFTurbo): https://github.com/ZFTurbo/'

import torch
import numpy as np
import ml_collections
import yaml
from pathlib import Path
import os

# 避免循环导入，先定义必要的函数

def get_model_from_config(model_type, config_path):
    """
    从配置文件中获取模型
    """
    with open(config_path, 'r') as stream:
        config = ml_collections.ConfigDict(yaml.safe_load(stream))
    
    if model_type == 'bs_roformer':
        from models.bs_roformer import BSRoformer
        model = BSRoformer(**config.model)
    elif model_type == 'mel_band_roformer':
        from models.mel_band_roformer import MelBandRoformer
        model = MelBandRoformer(**config.model)
    elif model_type == 'segm_models':
        from models.segm_models import Segm_Models_Net
        model = Segm_Models_Net(**config.model)
    elif model_type == 'htdemucs':
        from models.demucs import htdemucs
        model = htdemucs(**config.model)
    elif model_type == 'swin_upernet':
        from models.swin_upernet import SwinUperNet
        model = SwinUperNet(**config.model)
    elif model_type == 'bandit':
        from models.bandit import Bandit
        model = Bandit(**config.model)
    elif model_type == 'mdx23c':
        from models.mdx23c import TFC_TDF_NET
        model = TFC_TDF_NET(**config.model)
    else:
        raise ValueError(f'Unknown model type: {model_type}')
    
    return model, config


def demix_track(config, model, mix, device):
    """
    使用模型对混音进行分离
    """
    C = config.audio.chunk_size
    N = config.inference.num_overlap
    hop = C // N
    
    # 将mix转换为正确的形状
    if len(mix.shape) == 1:
        mix = mix[None, :]
    elif len(mix.shape) == 2 and mix.shape[0] == 2:
        mix = mix[None, :]
    
    _, channels, length = mix.shape
    mix = mix.to(device)
    
    # 创建分离结果的容器
    instruments = config.training.instruments
    if hasattr(config.training, 'target_instrument') and config.training.target_instrument:
        instruments = [config.training.target_instrument]
    
    sources = {}
    for instrument in instruments:
        sources[instrument] = torch.zeros((channels, length), dtype=torch.float32, device=device)
    
    # 重叠窗口处理
    step = hop
    total_chunks = (length + step - 1) // step
    
    # 汉明窗口
    window = torch.hann_window(C, device=device)
    window = window[None, None, :]
    
    # 归一化因子
    norm_sum = torch.zeros((channels, length), dtype=torch.float32, device=device)
    
    with torch.no_grad():
        for i in range(0, length, step):
            start = i
            end = min(i + C, length)
            chunk_length = end - start
            
            # 提取chunk
            chunk = torch.zeros((1, channels, C), dtype=torch.float32, device=device)
            chunk[:, :, :chunk_length] = mix[:, :, start:end]
            
            # 模型推理
            output = model(chunk)
            
            # 如果输出是单个张量，需要转换为字典
            if isinstance(output, torch.Tensor):
                if len(instruments) == 1:
                    output = {instruments[0]: output}
                else:
                    # 假设输出的第一个维度对应不同的乐器
                    output = {inst: output[j] for j, inst in enumerate(instruments)}
            
            # 应用窗口并累加
            window_chunk = window[:, :, :chunk_length] if chunk_length < C else window
            
            for instrument in instruments:
                windowed_output = output[instrument] * window_chunk
                sources[instrument][:, start:end] += windowed_output[0, :, :chunk_length]
            
            # 累加归一化因子
            norm_sum[:, start:end] += window_chunk[0, :, :chunk_length]
    
    # 归一化
    for instrument in instruments:
        sources[instrument] = sources[instrument] / (norm_sum + 1e-8)
    
    return sources


def demix_track_demucs(config, model, mix, device):
    """
    使用Demucs模型进行分离
    """
    from demucs import apply_model
    
    mix = mix.to(device)
    if len(mix.shape) == 2:
        mix = mix[None, :]
    
    # 应用Demucs模型
    sources = apply_model(model, mix, device=device)
    
    # 转换为字典格式
    instruments = config.training.instruments
    result = {}
    for i, instrument in enumerate(instruments):
        result[instrument] = sources[0, i, :, :]
    
    return result


def normalize_audio(audio, target_lufs=-23.0):
    """
    归一化音频到目标响度
    """
    import pyloudnorm as pyln
    
    # 计算当前响度
    meter = pyln.Meter(44100)
    loudness = meter.integrated_loudness(audio.T)
    
    # 归一化到目标响度
    if not np.isfinite(loudness):
        return audio
    
    gain = target_lufs - loudness
    normalized_audio = audio * (10.0 ** (gain / 20.0))
    
    return normalized_audio


def chunk_audio(audio, chunk_size, hop_size):
    """
    将音频分割成重叠的块
    """
    chunks = []
    for i in range(0, audio.shape[-1] - chunk_size + 1, hop_size):
        chunk = audio[..., i:i + chunk_size]
        chunks.append(chunk)
    
    return chunks


def overlap_add(chunks, hop_size, total_length):
    """
    使用重叠相加方法重建音频
    """
    if not chunks:
        return np.zeros((2, total_length))
    
    result = np.zeros((chunks[0].shape[0], total_length))
    
    for i, chunk in enumerate(chunks):
        start = i * hop_size
        end = start + chunk.shape[-1]
        if end > total_length:
            chunk = chunk[..., :total_length - start]
            end = total_length
        
        result[..., start:end] += chunk
    
    return result 