.tour-backdrop {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1030;
  background-color: #000;
  opacity: 0.8;
}

.tour-step-backdrop {
  position: relative;
  z-index: 1031;
  background: inherit;
}

.tour-step-background {
  position: absolute;
  z-index: 1030;
  background: inherit;
  border-radius: 6px;
}

.popover[class*="tour-"] {
  z-index: 1030;

  .popover-navigation {
    padding: 9px 14px;

    *[data-role=end]{
      float: right;
    }

    *[data-role=prev],
    *[data-role=next],
    *[data-role=end]{
      cursor: pointer;

      &.disabled {
        cursor: default;
      }
    }
  }

  &.orphan {
    position: fixed;
    margin-top: 0;

    .arrow {
      display: none;
    }
  }
}
