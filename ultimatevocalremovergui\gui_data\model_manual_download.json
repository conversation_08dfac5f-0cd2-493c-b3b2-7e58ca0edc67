{"current_version": "UVR_Patch_3_31_23_5_5", "current_version_mac": "UVR_Patch_3_31_23_5_5", "current_version_linux": "UVR_Patch_3_31_23_5_5", "vr_download_list": {"VR Arch Single Model v5: 1_HP-UVR": "1_HP-UVR.pth", "VR Arch Single Model v5: 2_HP-UVR": "2_HP-UVR.pth", "VR Arch Single Model v5: 3_HP-Vocal-UVR": "3_HP-Vocal-UVR.pth", "VR Arch Single Model v5: 4_HP-Vocal-UVR": "4_HP-Vocal-UVR.pth", "VR Arch Single Model v5: 5_HP-Karaoke-UVR": "5_HP-Karaoke-UVR.pth", "VR Arch Single Model v5: 6_HP-Karaoke-UVR": "6_HP-Karaoke-UVR.pth", "VR Arch Single Model v5: 7_HP2-UVR": "7_HP2-UVR.pth", "VR Arch Single Model v5: 8_HP2-UVR": "8_HP2-UVR.pth", "VR Arch Single Model v5: 9_HP2-UVR": "9_HP2-UVR.pth", "VR Arch Single Model v5: 10_SP-UVR-2B-32000-1": "10_SP-UVR-2B-32000-1.pth", "VR Arch Single Model v5: 11_SP-UVR-2B-32000-2": "11_SP-UVR-2B-32000-2.pth", "VR Arch Single Model v5: 12_SP-UVR-3B-44100": "12_SP-UVR-3B-44100.pth", "VR Arch Single Model v5: 13_SP-UVR-4B-44100-1": "13_SP-UVR-4B-44100-1.pth", "VR Arch Single Model v5: 14_SP-UVR-4B-44100-2": "14_SP-UVR-4B-44100-2.pth", "VR Arch Single Model v5: 15_SP-UVR-MID-44100-1": "15_SP-UVR-MID-44100-1.pth", "VR Arch Single Model v5: 16_SP-UVR-MID-44100-2": "16_SP-UVR-MID-44100-2.pth", "VR Arch Single Model v5: 17_HP-Wind_Inst-UVR": "17_HP-Wind_Inst-UVR.pth", "VR Arch Single Model v5: UVR-De-Echo-Aggressive by FoxJoy": "UVR-De-Echo-Aggressive.pth", "VR Arch Single Model v5: UVR-De-Echo-Normal by FoxJoy": "UVR-De-Echo-Normal.pth", "VR Arch Single Model v5: UVR-DeEcho-DeReverb by FoxJoy": "UVR-DeEcho-DeReverb.pth", "VR Arch Single Model v5: UVR-DeNoise-Lite by FoxJoy": "UVR-DeNoise-Lite.pth", "VR Arch Single Model v5: UVR-DeNoise by FoxJoy": "UVR-DeNoise.pth", "VR Arch Single Model v5: UVR-BVE-4B_SN-44100-1": "UVR-BVE-4B_SN-44100-1.pth", "VR Arch Single Model v4: MGM_HIGHEND_v4": "MGM_HIGHEND_v4.pth", "VR Arch Single Model v4: MGM_LOWEND_A_v4": "MGM_LOWEND_A_v4.pth", "VR Arch Single Model v4: MGM_LOWEND_B_v4": "MGM_LOWEND_B_v4.pth", "VR Arch Single Model v4: MGM_MAIN_v4": "MGM_MAIN_v4.pth"}, "mdx_download_list": {"MDX-Net Model: UVR-MDX-NET Inst HQ 1": "UVR-MDX-NET-Inst_HQ_1.onnx", "MDX-Net Model: UVR-MDX-NET Inst HQ 2": "UVR-MDX-NET-Inst_HQ_2.onnx", "MDX-Net Model: UVR-MDX-NET Inst HQ 3": "UVR-MDX-NET-Inst_HQ_3.onnx", "MDX-Net Model: UVR-MDX-NET Main": "UVR_MDXNET_Main.onnx", "MDX-Net Model: UVR-MDX-NET Inst Main": "UVR-MDX-NET-Inst_Main.onnx", "MDX-Net Model: UVR-MDX-NET 1": "UVR_MDXNET_1_9703.onnx", "MDX-Net Model: UVR-MDX-NET 2": "UVR_MDXNET_2_9682.onnx", "MDX-Net Model: UVR-MDX-NET 3": "UVR_MDXNET_3_9662.onnx", "MDX-Net Model: UVR-MDX-NET Inst 1": "UVR-MDX-NET-Inst_1.onnx", "MDX-Net Model: UVR-MDX-NET Inst 2": "UVR-MDX-NET-Inst_2.onnx", "MDX-Net Model: UVR-MDX-NET Inst 3": "UVR-MDX-NET-Inst_3.onnx", "MDX-Net Model: UVR-MDX-NET Karaoke": "UVR_MDXNET_KARA.onnx", "MDX-Net Model: UVR-MDX-NET Karaoke 2": "UVR_MDXNET_KARA_2.onnx", "MDX-Net Model: UVR_MDXNET_9482": "UVR_MDXNET_9482.onnx", "MDX-Net Model: UVR-MDX-NET Voc FT": "UVR-MDX-NET-Voc_FT.onnx", "MDX-Net Model: Kim Vocal 1": "Kim_Vocal_1.onnx", "MDX-Net Model: Kim Vocal 2": "Kim_Vocal_2.onnx", "MDX-Net Model: Kim Inst": "Kim_Inst.onnx", "MDX-Net Model: Reverb HQ By FoxJoy": "Reverb_HQ_By_FoxJoy.onnx", "MDX-Net Model: kuielab_a_vocals": "kui<PERSON>b_a_vocals.onnx", "MDX-Net Model: kuielab_a_other": "kuielab_a_other.onnx", "MDX-Net Model: kuielab_a_bass": "kuielab_a_bass.onnx", "MDX-Net Model: kuielab_a_drums": "kuielab_a_drums.onnx", "MDX-Net Model: kuielab_b_vocals": "kui<PERSON><PERSON>_b_vocals.onnx", "MDX-Net Model: kuielab_b_other": "kuielab_b_other.onnx", "MDX-Net Model: kuielab_b_bass": "kui<PERSON>b_b_bass.onnx", "MDX-Net Model: kuielab_b_drums": "kuielab_b_drums.onnx"}, "demucs_download_list": {"Demucs v4: htdemucs_ft": {"f7e0c4bc-ba3fe64a.th": "https://dl.fbaipublicfiles.com/demucs/hybrid_transformer/f7e0c4bc-ba3fe64a.th", "d12395a8-e57c48e6.th": "https://dl.fbaipublicfiles.com/demucs/hybrid_transformer/d12395a8-e57c48e6.th", "92cfc3b6-ef3bcb9c.th": "https://dl.fbaipublicfiles.com/demucs/hybrid_transformer/92cfc3b6-ef3bcb9c.th", "04573f0d-f3cf25b2.th": "https://dl.fbaipublicfiles.com/demucs/hybrid_transformer/04573f0d-f3cf25b2.th", "htdemucs_ft.yaml": "https://github.com/TRvlvr/model_repo/releases/download/all_public_uvr_models/htdemucs_ft.yaml"}, "Demucs v4: htdemucs": {"955717e8-8726e21a.th": "https://dl.fbaipublicfiles.com/demucs/hybrid_transformer/955717e8-8726e21a.th", "htdemucs.yaml": "https://github.com/TRvlvr/model_repo/releases/download/all_public_uvr_models/htdemucs.yaml"}, "Demucs v4: hdemucs_mmi": {"75fc33f5-1941ce65.th": "https://dl.fbaipublicfiles.com/demucs/hybrid_transformer/75fc33f5-1941ce65.th", "hdemucs_mmi.yaml": "https://github.com/TRvlvr/model_repo/releases/download/all_public_uvr_models/hdemucs_mmi.yaml"}, "Demucs v4: htdemucs_6s": {"5c90dfd2-34c22ccb.th": "https://dl.fbaipublicfiles.com/demucs/hybrid_transformer/5c90dfd2-34c22ccb.th", "htdemucs_6s.yaml": "https://github.com/TRvlvr/model_repo/releases/download/all_public_uvr_models/htdemucs_6s.yaml"}, "Demucs v3: mdx": {"0d19c1c6-0f06f20e.th": "https://dl.fbaipublicfiles.com/demucs/mdx_final/0d19c1c6-0f06f20e.th", "7ecf8ec1-70f50cc9.th": "https://dl.fbaipublicfiles.com/demucs/mdx_final/7ecf8ec1-70f50cc9.th", "c511e2ab-fe698775.th": "https://dl.fbaipublicfiles.com/demucs/mdx_final/c511e2ab-fe698775.th", "7d865c68-3d5dd56b.th": "https://dl.fbaipublicfiles.com/demucs/mdx_final/7d865c68-3d5dd56b.th", "mdx.yaml": "https://raw.githubusercontent.com/facebookresearch/demucs/main/demucs/remote/mdx.yaml"}, "Demucs v3: mdx_q": {"6b9c2ca1-3fd82607.th": "https://dl.fbaipublicfiles.com/demucs/mdx_final/6b9c2ca1-3fd82607.th", "b72baf4e-8778635e.th": "https://dl.fbaipublicfiles.com/demucs/mdx_final/b72baf4e-8778635e.th", "42e558d4-196e0e1b.th": "https://dl.fbaipublicfiles.com/demucs/mdx_final/42e558d4-196e0e1b.th", "305bc58f-18378783.th": "https://dl.fbaipublicfiles.com/demucs/mdx_final/305bc58f-18378783.th", "mdx_q.yaml": "https://raw.githubusercontent.com/facebookresearch/demucs/main/demucs/remote/mdx_q.yaml"}, "Demucs v3: mdx_extra": {"e51eebcc-c1b80bdd.th": "https://dl.fbaipublicfiles.com/demucs/mdx_final/e51eebcc-c1b80bdd.th", "a1d90b5c-ae9d2452.th": "https://dl.fbaipublicfiles.com/demucs/mdx_final/a1d90b5c-ae9d2452.th", "5d2d6c55-db83574e.th": "https://dl.fbaipublicfiles.com/demucs/mdx_final/5d2d6c55-db83574e.th", "cfa93e08-61801ae1.th": "https://dl.fbaipublicfiles.com/demucs/mdx_final/cfa93e08-61801ae1.th", "mdx_extra.yaml": "https://raw.githubusercontent.com/facebookresearch/demucs/main/demucs/remote/mdx_extra.yaml"}, "Demucs v3: mdx_extra_q": {"83fc094f-4a16d450.th": "https://dl.fbaipublicfiles.com/demucs/mdx_final/83fc094f-4a16d450.th", "464b36d7-e5a9386e.th": "https://dl.fbaipublicfiles.com/demucs/mdx_final/464b36d7-e5a9386e.th", "14fc6a69-a89dd0ee.th": "https://dl.fbaipublicfiles.com/demucs/mdx_final/14fc6a69-a89dd0ee.th", "7fd6ef75-a905dd85.th": "https://dl.fbaipublicfiles.com/demucs/mdx_final/7fd6ef75-a905dd85.th", "mdx_extra_q.yaml": "https://raw.githubusercontent.com/facebookresearch/demucs/main/demucs/remote/mdx_extra_q.yaml"}, "Demucs v3: UVR Model": {"ebf34a2db.th": "https://github.com/TRvlvr/model_repo/releases/download/all_public_uvr_models/ebf34a2db.th", "UVR_Demucs_Model_1.yaml": "https://github.com/TRvlvr/model_repo/releases/download/all_public_uvr_models/UVR_Demucs_Model_1.yaml"}, "Demucs v3: repro_mdx_a": {"9a6b4851-03af0aa6.th": "https://dl.fbaipublicfiles.com/demucs/mdx_final/9a6b4851-03af0aa6.th", "1ef250f1-592467ce.th": "https://dl.fbaipublicfiles.com/demucs/mdx_final/1ef250f1-592467ce.th", "fa0cb7f9-100d8bf4.th": "https://dl.fbaipublicfiles.com/demucs/mdx_final/fa0cb7f9-100d8bf4.th", "902315c2-b39ce9c9.th": "https://dl.fbaipublicfiles.com/demucs/mdx_final/902315c2-b39ce9c9.th", "repro_mdx_a.yaml": "https://github.com/TRvlvr/model_repo/releases/download/all_public_uvr_models/repro_mdx_a.yaml"}, "Demucs v3: repro_mdx_a_time_only": {"9a6b4851-03af0aa6.th": "https://dl.fbaipublicfiles.com/demucs/mdx_final/9a6b4851-03af0aa6.th", "1ef250f1-592467ce.th": "https://dl.fbaipublicfiles.com/demucs/mdx_final/1ef250f1-592467ce.th", "repro_mdx_a_time_only.yaml": "https://github.com/TRvlvr/model_repo/releases/download/all_public_uvr_models/repro_mdx_a_time_only.yaml"}, "Demucs v3: repro_mdx_a_hybrid_only": {"fa0cb7f9-100d8bf4.th": "https://dl.fbaipublicfiles.com/demucs/mdx_final/fa0cb7f9-100d8bf4.th", "902315c2-b39ce9c9.th": "https://dl.fbaipublicfiles.com/demucs/mdx_final/902315c2-b39ce9c9.th", "repro_mdx_a_hybrid_only.yaml": "https://github.com/TRvlvr/model_repo/releases/download/all_public_uvr_models/repro_mdx_a_hybrid_only.yaml"}, "Demucs v2: demucs": {"demucs-e07c671f.th": "https://dl.fbaipublicfiles.com/demucs/v3.0/demucs-e07c671f.th"}, "Demucs v2: demucs_extra": {"demucs_extra-3646af93.th": "https://dl.fbaipublicfiles.com/demucs/v3.0/demucs_extra-3646af93.th"}, "Demucs v2: demucs48_hq": {"demucs48_hq-28a1282c.th": "https://dl.fbaipublicfiles.com/demucs/v3.0/demucs48_hq-28a1282c.th"}, "Demucs v2: tasnet": {"tasnet-beb46fac.th": "https://dl.fbaipublicfiles.com/demucs/v3.0/tasnet-beb46fac.th"}, "Demucs v2: tasnet_extra": {"tasnet_extra-df3777b2.th": "https://dl.fbaipublicfiles.com/demucs/v3.0/tasnet_extra-df3777b2.th"}, "Demucs v2: demucs_unittest": {"demucs_unittest-09ebc15f.th": "https://dl.fbaipublicfiles.com/demucs/v3.0/demucs_unittest-09ebc15f.th"}, "Demucs v1: demucs": {"demucs.th": "https://dl.fbaipublicfiles.com/demucs/v2.0/demucs.th"}, "Demucs v1: demucs_extra": {"demucs_extra.th": "https://dl.fbaipublicfiles.com/demucs/v2.0/demucs_extra.th"}, "Demucs v1: light": {"light.th": "https://dl.fbaipublicfiles.com/demucs/v2.0/light.th"}, "Demucs v1: light_extra": {"light_extra.th": "https://dl.fbaipublicfiles.com/demucs/v2.0/light_extra.th"}, "Demucs v1: tasnet": {"tasnet.th": "https://dl.fbaipublicfiles.com/demucs/v2.0/tasnet.th"}, "Demucs v1: tasnet_extra": {"tasnet_extra.th": "https://dl.fbaipublicfiles.com/demucs/v2.0/tasnet_extra.th"}}, "mdx_download_vip_list": {"MDX-Net Model VIP: UVR-MDX-NET_Main_340": "UVR-MDX-NET_Main_340.onnx", "MDX-Net Model VIP: UVR-MDX-NET_Main_390": "UVR-MDX-NET_Main_390.onnx", "MDX-Net Model VIP: UVR-MDX-NET_Main_406": "UVR-MDX-NET_Main_406.onnx", "MDX-Net Model VIP: UVR-MDX-NET_Main_427": "UVR-MDX-NET_Main_427.onnx", "MDX-Net Model VIP: UVR-MDX-NET_Main_438": "UVR-MDX-NET_Main_438.onnx", "MDX-Net Model VIP: UVR-MDX-NET_Inst_82_beta": "UVR-MDX-NET_Inst_82_beta.onnx", "MDX-Net Model VIP: UVR-MDX-NET_Inst_90_beta": "UVR-MDX-NET_Inst_90_beta.onnx", "MDX-Net Model VIP: UVR-MDX-NET_Inst_187_beta": "UVR-MDX-NET_Inst_187_beta.onnx", "MDX-Net Model VIP: UVR-MDX-NET-Inst_full_292": "UVR-MDX-NET-Inst_full_292.onnx"}, "mdx23_download_list": {"MDX23 Model: MDX23C_D1581": {"MDX23C_D1581.ckpt": "model_2_stem_061321.yaml"}}, "vr_download_vip_list": [], "demucs_download_vip_list": []}