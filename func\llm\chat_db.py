from bson.json_util import dumps
from flask import jsonify
from func.database.mongodb import Mongodb
from func.tools.singleton_mode import singleton
# from func.tools.decorator_mode import class_switch
from func.log.default_log import DefaultLog

log = DefaultLog().getLogger()

@singleton
class ChatDB:
    def __init__(self):
        self.Mongodb = Mongodb()
        self.db = self.Mongodb.get_db()
        self.chatlistDB = self.db['chatlist']
        self.chat_record = self.db['chat_record']
        self.chat_request = self.db['chat_request']
        
    def insert_chat(self, data):
        """
        插入聊天记录
        :param data: 聊天数据
        :return:
        """
        try:
            self.chat_record.insert_one(data)
            return True
        except Exception as e:
            print(e)
            log.error(f"插入聊天记录失败: {str(e)}")
            return False

    def all_chat(self, username):
        """
        获取用户所有聊天记录
        :param username: 用户名
        :return:
        """
        try:
            cursor = self.chat_record.find({"username": username}).sort("submitTime", -1)
            return list(cursor)
        except Exception as e:
            print(e)
            log.error(f"获取聊天记录失败: {str(e)}")
            return []

    def insert_request(self, data):
        """
        插入请求记录
        :param data: 请求数据
        :return:
        """
        try:
            print("---chatdb---插入---insert_request---")
            # 确保数据是字典类型
            if not isinstance(data, dict):
                try:
                    # 尝试将数据转换为字典
                    if hasattr(data, '__dict__'):
                        data = data.__dict__
                    else:
                        data = dict(data)
                except Exception as e:
                    log.error(f"数据转换为字典失败: {str(e)}")
                    return False
                
            self.chat_request.insert_one(data)
            return True
        except Exception as e:
            print(e)
            log.error(f"插入请求记录失败: {str(e)}")
            return False
    # userinfo_controller调用，无openid
    def find_chat_list_page(self, username, page_number=1, page_size=10):
        """
        聊天列表分页查询
        :param username: 用户名
        :param page_number: 页码
        :param page_size: 每页大小
        :return:
        """
        try:
            skip_count = (page_number - 1) * page_size
            cursor = self.chat_request.find(
                {"user_name": {"$regex": username}}#, "$options": "i"
            ).sort("submitTime", -1).skip(skip_count).limit(page_size)

            total_documents = self.chat_request.count_documents(
                {"user_name": {"$regex": username, "$options": "i"}}
            )
            total_pages = (total_documents + page_size - 1) // page_size
            
            return {
                "status": 200,
                "data": list(cursor),
                "total_documents": total_documents,
                "total_pages": total_pages,
                "current_page": page_number,
                "page_size": page_size
            }
        except Exception as e:
            print(e)
            log.error(f"查询聊天列表失败: {str(e)}")
            return None