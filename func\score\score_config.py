"""
积分系统配置模块
用于管理积分系统的所有配置参数
"""

from enum import Enum
from typing import Dict, Any
import json
import os

class ScoreOperationType(Enum):
    """积分操作类型枚举"""
    # chat = "聊天"
    # sing = "唱歌"  
    # dance = "跳舞"
    # draw = "绘画"
    # cut_sing = "切歌"
    # cut_dance = "切舞"
    # recharge = "充值"
    # deduct = "扣减"
    # gift = "礼物"
    # system = "系统奖励"
    # penalty = "系统扣分"
    # daily_bouns = "每日签到"
    # activity = "活动奖励"

    chat = "chat"
    sing = "sing"  
    dance = "dance"
    draw = "draw"
    cut_sing = "cut_sing"
    cut_dance = "cut_dance"
    recharge = "recharge"
    deduct = "deduct"
    gift = "gift"
    system = "system"
    penalty = "penalty"
    daily_bouns = "daily_bouns"
    activity = "activity"

class ScoreSystemConfig:
    """积分系统配置类"""
    
    # ============= 基础积分配置 =============
    CHAT_SCORE = 1              # 聊天获得积分
    SING_COST = 2              # 唱歌消耗积分
    DANCE_COST = 3             # 跳舞消耗积分
    DRAW_COST = 1              # 绘画消耗积分
    CUT_SONG_COST = 1          # 切歌消耗积分
    CUT_DANCE_COST = 1         # 切舞消耗积分
    
    # ============= 积分限制配置 =============
    MAX_DAILY_CHAT_SCORE = 100      # 每日聊天最大获得积分
    MIN_SCORE_FOR_ACTION = 0        # 执行操作的最小积分要求
    MAX_SINGLE_OPERATION = 10000    # 单次操作最大积分数量
    MIN_SCORE_BALANCE = 0           # 最小积分余额（不能为负）
    
    # ============= 分页和查询配置 =============
    DEFAULT_PAGE_SIZE = 10          # 默认分页大小
    MAX_PAGE_SIZE = 100            # 最大分页大小
    MAX_RANK_LIMIT = 1000          # 排行榜最大查询数量
    
    # ============= 缓存配置 =============
    SCORE_CACHE_TTL = 300          # 积分缓存时间（秒）
    RANK_CACHE_TTL = 600           # 排行榜缓存时间（秒）
    
    # ============= 数据库配置 =============
    BATCH_OPERATION_SIZE = 100     # 批量操作大小
    INDEX_CREATION_TIMEOUT = 30    # 索引创建超时时间（秒）
    
    # ============= 安全配置 =============
    MAX_USERNAME_LENGTH = 50       # 用户名最大长度
    MAX_OPERATION_LENGTH = 20      # 操作类型最大长度
    RATE_LIMIT_PER_MINUTE = 60     # 每分钟操作次数限制
    
    # ============= 业务规则配置 =============
    DAILY_SIGN_BONUS = 5          # 每日签到奖励积分
    CONSECUTIVE_SIGN_BONUS = 2    # 连续签到额外奖励
    GIFT_SCORE_MULTIPLIER = 10    # 礼物积分倍数
    
    @classmethod
    def get_operation_score(cls, operation: str) -> int:
        """
        获取操作对应的积分变化
        :param operation: 操作类型
        :return: 积分变化（正数为奖励，负数为消耗）
        """
        operation_scores = {
            ScoreOperationType.chat.value: cls.CHAT_SCORE,
            ScoreOperationType.sing.value: -cls.SING_COST,
            ScoreOperationType.dance.value: -cls.DANCE_COST,
            ScoreOperationType.draw.value: -cls.DRAW_COST,
            ScoreOperationType.cut_sing.value: -cls.CUT_SONG_COST,
            ScoreOperationType.cut_dance.value: -cls.CUT_DANCE_COST,
            ScoreOperationType.daily_bouns.value: cls.DAILY_SIGN_BONUS,
        }
        return operation_scores.get(operation, 0)
    
    @classmethod
    def is_consumption_operation(cls, operation: str) -> bool:
        """
        判断是否为消耗型操作
        :param operation: 操作类型
        :return: 是否为消耗型操作
        """
        consumption_operations = {
            ScoreOperationType.sing.value,
            ScoreOperationType.dance.value,
            ScoreOperationType.draw.value,
            ScoreOperationType.cut_sing.value,
            ScoreOperationType.cut_dance.value,
        }
        return operation in consumption_operations
    
    @classmethod
    def validate_score_amount(cls, score: int) -> bool:
        """
        验证积分数量是否合法
        :param score: 积分数量
        :return: 是否合法
        """
        return abs(score) <= cls.MAX_SINGLE_OPERATION
    
    @classmethod
    def validate_operation_type(cls, operation: str) -> bool:
        """
        验证操作类型是否合法
        :param operation: 操作类型
        :return: 是否合法
        """
        valid_operations = {op.value for op in ScoreOperationType}
        return operation in valid_operations
    
    @classmethod
    def get_config_dict(cls) -> Dict[str, Any]:
        """
        获取配置字典
        :return: 配置字典
        """
        return {
            "score_rules": {
                "chat_score": cls.CHAT_SCORE,
                "sing_cost": cls.SING_COST,
                "dance_cost": cls.DANCE_COST,
                "draw_cost": cls.DRAW_COST,
                "cut_song_cost": cls.CUT_SONG_COST,
                "cut_dance_cost": cls.CUT_DANCE_COST,
            },
            "limits": {
                "max_daily_chat_score": cls.MAX_DAILY_CHAT_SCORE,
                "min_score_for_action": cls.MIN_SCORE_FOR_ACTION,
                "max_single_operation": cls.MAX_SINGLE_OPERATION,
                "min_score_balance": cls.MIN_SCORE_BALANCE,
            },
            "pagination": {
                "default_page_size": cls.DEFAULT_PAGE_SIZE,
                "max_page_size": cls.MAX_PAGE_SIZE,
                "max_rank_limit": cls.MAX_RANK_LIMIT,
            },
            "cache": {
                "score_cache_ttl": cls.SCORE_CACHE_TTL,
                "rank_cache_ttl": cls.RANK_CACHE_TTL,
            },
            "business": {
                "daily_sign_bonus": cls.DAILY_SIGN_BONUS,
                "consecutive_sign_bonus": cls.CONSECUTIVE_SIGN_BONUS,
                "gift_score_multiplier": cls.GIFT_SCORE_MULTIPLIER,
            }
        }

class ScoreRankConfig:
    """积分排行榜配置"""
    
    # 排行榜标题
    RANK_TITLES = {
        1: "冠军",
        2: "亚军", 
        3: "季军"
    }
    
    # 默认排行榜显示数量
    DEFAULT_RANK_DISPLAY = 10
    MAX_RANK_DISPLAY = 100
    
    @classmethod
    def get_rank_title(cls, rank: int) -> str:
        """
        获取排名标题
        :param rank: 排名
        :return: 标题
        """
        if rank in cls.RANK_TITLES:
            return cls.RANK_TITLES[rank]
        return f"第{rank}名"

class ScoreMessageConfig:
    """积分消息配置"""
    
    # 成功消息模板
    SUCCESS_MESSAGES = {
        "recharge": "积分充值成功！当前积分：{score}",
        "deduct": "积分扣减成功！当前积分：{score}",
        "chat": "聊天获得积分：+{score}",
        "sing": "唱歌消耗积分：-{score}",
        "dance": "跳舞消耗积分：-{score}",
        "draw": "绘画消耗积分：-{score}",
    }
    
    # 错误消息模板
    ERROR_MESSAGES = {
        "insufficient_score": "积分不足，当前积分：{score}，需要：{required}",
        "user_not_found": "用户不存在",
        "invalid_operation": "无效的操作类型：{operation}",
        "daily_limit_reached": "今日聊天积分已达上限",
        "score_limit_exceeded": "积分数量超出限制",
    }
    
    @classmethod
    def get_success_message(cls, operation: str, **kwargs) -> str:
        """
        获取成功消息
        :param operation: 操作类型
        :param kwargs: 参数
        :return: 消息
        """
        template = cls.SUCCESS_MESSAGES.get(operation, "操作成功")
        return template.format(**kwargs)
    
    @classmethod
    def get_error_message(cls, error_type: str, **kwargs) -> str:
        """
        获取错误消息
        :param error_type: 错误类型
        :param kwargs: 参数
        :return: 消息
        """
        template = cls.ERROR_MESSAGES.get(error_type, "操作失败")
        return template.format(**kwargs)

def load_config_from_file(config_path: str) -> Dict[str, Any]:
    """
    从文件加载配置
    :param config_path: 配置文件路径
    :return: 配置字典
    """
    if os.path.exists(config_path):
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"加载配置文件失败: {e}")
    return {}

def save_config_to_file(config: Dict[str, Any], config_path: str) -> bool:
    """
    保存配置到文件
    :param config: 配置字典
    :param config_path: 配置文件路径
    :return: 是否成功
    """
    try:
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        print(f"保存配置文件失败: {e}")
        return False 