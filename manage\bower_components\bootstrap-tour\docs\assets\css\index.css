/* Add additional stylesheets below
-------------------------------------------------- */
/*
  <PERSON>trap's documentation styles
  Special styles for presenting <PERSON><PERSON><PERSON>'s documentation and examples
*/



/* Body and structure
-------------------------------------------------- */

body {
  position: relative;
  padding-top: 40px;
  padding-bottom: 40px;
}

/* Code in headings */
h3 code {
  font-size: 14px;
  font-weight: normal;
}



/* Tweak navbar brand link to be super sleek
-------------------------------------------------- */

/* Change the docs' brand */
body > .navbar .brand {
  padding-right: 0;
  padding-left: 0;
  margin-left: 20px;
  float: right;
  font-weight: bold;
  color: #000;
  text-shadow: 0 1px 0 rgba(255,255,255,.1), 0 0 30px rgba(255,255,255,.125);
  -webkit-transition: all .2s linear;
     -moz-transition: all .2s linear;
          transition: all .2s linear;
}
body > .navbar .brand:hover {
  text-decoration: none;
  text-shadow: 0 1px 0 rgba(255,255,255,.1), 0 0 30px rgba(255,255,255,.4);
}


/* Sections
-------------------------------------------------- */

/* padding for in-page bookmarks and fixed navbar */
section {
  padding-top: 30px;
}
section > .page-header,
section > .lead {
  color: #5a5a5a;
}
section > ul li {
  margin-bottom: 5px;
}

/* Separators (hr) */
.bs-docs-separator {
  margin: 40px 0 39px;
}

/* Faded out hr */
hr.soften {
  height: 1px;
  margin: 70px 0;
  background-image: -webkit-linear-gradient(left, rgba(0,0,0,0), rgba(0,0,0,.1), rgba(0,0,0,0));
  background-image:    -moz-linear-gradient(left, rgba(0,0,0,0), rgba(0,0,0,.1), rgba(0,0,0,0));
  background-image:     -ms-linear-gradient(left, rgba(0,0,0,0), rgba(0,0,0,.1), rgba(0,0,0,0));
  background-image:      -o-linear-gradient(left, rgba(0,0,0,0), rgba(0,0,0,.1), rgba(0,0,0,0));
  border: 0;
}

.bs-callout {
  margin: 20px 0;
  padding: 20px;
  border-left: 3px solid #eee;
}
.bs-callout-warning {
  background-color: #faf8f0;
  border-color: #faebcc;
}
.bs-callout-warning h4 {
  color: #c09853;
}
.bs-callout h4 {
  margin-top: 0;
  margin-bottom: 5px;
}
.bs-callout p:last-child {
  margin-bottom: 0;
}


/* Jumbotrons
-------------------------------------------------- */

/* Base class
------------------------- */
.jumbotron {
  position: relative;
  padding: 40px 0;
  font-size: 16px;
  color: #fff;
  text-align: center;
  text-shadow: 0 1px 3px rgba(0,0,0,.4), 0 0 30px rgba(0,0,0,.075);
  background: #020031; /* Old browsers */
  background: -moz-linear-gradient(45deg,  #020031 0%, #6d3353 100%); /* FF3.6+ */
  background: -webkit-gradient(linear, left bottom, right top, color-stop(0%,#020031), color-stop(100%,#6d3353)); /* Chrome,Safari4+ */
  background: -webkit-linear-gradient(45deg,  #020031 0%,#6d3353 100%); /* Chrome10+,Safari5.1+ */
  background: -o-linear-gradient(45deg,  #020031 0%,#6d3353 100%); /* Opera 11.10+ */
  background: -ms-linear-gradient(45deg,  #020031 0%,#6d3353 100%); /* IE10+ */
  background: linear-gradient(45deg,  #020031 0%,#6d3353 100%); /* W3C */
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#020031', endColorstr='#6d3353',GradientType=1 ); /* IE6-9 fallback on horizontal gradient */
  -webkit-box-shadow: inset 0 3px 7px rgba(0,0,0,.2), inset 0 -3px 7px rgba(0,0,0,.2);
     -moz-box-shadow: inset 0 3px 7px rgba(0,0,0,.2), inset 0 -3px 7px rgba(0,0,0,.2);
          box-shadow: inset 0 3px 7px rgba(0,0,0,.2), inset 0 -3px 7px rgba(0,0,0,.2);
}
.jumbotron h1 {
  font-size: 80px;
  letter-spacing: -1px;
  line-height: 1;
}
.jumbotron p {
  font-size: 24px;
  font-weight: 300;
  line-height: 1.25;
  margin-bottom: 30px;
}

/* Link styles (used on .masthead-links as well) */
.jumbotron a {
  color: #fff;
  color: rgba(255,255,255,.5);
  -webkit-transition: all .2s ease-in-out;
     -moz-transition: all .2s ease-in-out;
          transition: all .2s ease-in-out;
}
.jumbotron a:hover {
  color: #fff;
  text-shadow: 0 0 10px rgba(255,255,255,.25);
}

/* Download button */
.masthead .btn {
  padding: 19px 24px;
  font-size: 24px;
  font-weight: 200;
  border: 0;
  -webkit-border-radius: 6px;
     -moz-border-radius: 6px;
          border-radius: 6px;
  -webkit-box-shadow: inset 0 1px 0 rgba(255,255,255,.1), 0 1px 5px rgba(0,0,0,.25);
     -moz-box-shadow: inset 0 1px 0 rgba(255,255,255,.1), 0 1px 5px rgba(0,0,0,.25);
          box-shadow: inset 0 1px 0 rgba(255,255,255,.1), 0 1px 5px rgba(0,0,0,.25);
  -webkit-transition: none;
     -moz-transition: none;
          transition: none;
}
.masthead .btn:hover {
  -webkit-box-shadow: inset 0 1px 0 rgba(255,255,255,.1), 0 1px 5px rgba(0,0,0,.25);
     -moz-box-shadow: inset 0 1px 0 rgba(255,255,255,.1), 0 1px 5px rgba(0,0,0,.25);
          box-shadow: inset 0 1px 0 rgba(255,255,255,.1), 0 1px 5px rgba(0,0,0,.25);
}
.masthead .btn:active {
  -webkit-box-shadow: inset 0 2px 4px rgba(0,0,0,.1), 0 1px 0 rgba(255,255,255,.1);
     -moz-box-shadow: inset 0 2px 4px rgba(0,0,0,.1), 0 1px 0 rgba(255,255,255,.1);
          box-shadow: inset 0 2px 4px rgba(0,0,0,.1), 0 1px 0 rgba(255,255,255,.1);
}
#download {
  color: white;
}
#demo {
  color: #333;
}


/* Pattern overlay
------------------------- */
.jumbotron .container {
  position: relative;
  z-index: 2;
}
/*
.jumbotron:after {
  content: '';
  display: block;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: url(/assets/img/masthead-pattern.png) repeat center center;
  opacity: .4;
}
*/

@media
only screen and (-webkit-min-device-pixel-ratio: 2),
only screen and (   min--moz-device-pixel-ratio: 2),
only screen and (     -o-min-device-pixel-ratio: 2/1) {

  .jumbotron:after {
    background-size: 150px 150px;
  }

}

/* Masthead (docs home)
------------------------- */
.masthead {
  padding: 70px 0 80px;
  margin-bottom: 0;
  color: #fff;
}
.masthead h1 {
  font-size: 90px;
  line-height: 1;
  letter-spacing: -2px;
}
.masthead p {
  font-size: 30px;
  font-weight: 200;
  line-height: 1.25;
}

/* Textual links in masthead */
.masthead-links {
  margin: 0;
  list-style: none;
}
.masthead-links li {
  display: inline;
  padding: 0 10px;
  color: rgba(255,255,255,.25);
}

/* Social proof buttons from GitHub & Twitter */
.bs-docs-social {
  padding: 15px 0;
  text-align: center;
  background-color: #f5f5f5;
  border-top: 1px solid #fff;
  border-bottom: 1px solid #ddd;
}

/* Quick links on Home */
.bs-docs-social-buttons {
  margin-left: 0;
  margin-bottom: 0;
  padding-left: 0;
  list-style: none;
}
.bs-docs-social-buttons li {
  display: inline-block;
  padding: 5px 8px;
  line-height: 1;
  *display: inline;
  *zoom: 1;
}

/* Subhead (other pages)
------------------------- */
.subhead {
  text-align: left;
  border-bottom: 1px solid #ddd;
}
.subhead h1 {
  font-size: 60px;
}
.subhead p {
  margin-bottom: 20px;
}
.subhead .navbar {
  display: none;
}



/* Marketing section of Overview
-------------------------------------------------- */

.marketing {
  text-align: center;
  color: #5a5a5a;
}
.marketing h1 {
  margin: 60px 0 10px;
  font-size: 60px;
  font-weight: 200;
  line-height: 1;
  letter-spacing: -1px;
}
.marketing h2 {
  font-weight: 200;
  margin-bottom: 5px;
}
.marketing p {
  font-size: 16px;
  line-height: 1.5;
}
.marketing .marketing-byline {
  margin-bottom: 40px;
  font-size: 20px;
  font-weight: 300;
  line-height: 1.25;
  color: #999;
}
.marketing-img {
  display: block;
  margin: 0 auto 30px;
  max-height: 145px;
}



/* Footer
-------------------------------------------------- */

.footer {
  text-align: center;
  padding: 30px 0;
  margin-top: 70px;
  border-top: 1px solid #e5e5e5;
  background-color: #f5f5f5;
}
.footer p {
  margin-bottom: 0;
  color: #777;
}
.footer-links {
  margin: 10px 0;
}
.footer-links li {
  display: inline;
  padding: 0 2px;
}
.footer-links li:first-child {
  padding-left: 0;
}



/* Special grid styles
-------------------------------------------------- */

.show-grid {
  margin-top: 10px;
  margin-bottom: 20px;
}
.show-grid [class*="span"] {
  background-color: #eee;
  text-align: center;
  -webkit-border-radius: 3px;
     -moz-border-radius: 3px;
          border-radius: 3px;
  min-height: 40px;
  line-height: 40px;
}
.show-grid [class*="span"]:hover {
  background-color: #ddd;
}
.show-grid .show-grid {
  margin-top: 0;
  margin-bottom: 0;
}
.show-grid .show-grid [class*="span"] {
  margin-top: 5px;
}
.show-grid [class*="span"] [class*="span"] {
  background-color: #ccc;
}
.show-grid [class*="span"] [class*="span"] [class*="span"] {
  background-color: #999;
}



/* Mini layout previews
-------------------------------------------------- */
.mini-layout {
  border: 1px solid #ddd;
  -webkit-border-radius: 6px;
     -moz-border-radius: 6px;
          border-radius: 6px;
  -webkit-box-shadow: 0 1px 2px rgba(0,0,0,.075);
     -moz-box-shadow: 0 1px 2px rgba(0,0,0,.075);
          box-shadow: 0 1px 2px rgba(0,0,0,.075);
}
.mini-layout,
.mini-layout .mini-layout-body,
.mini-layout.fluid .mini-layout-sidebar {
  height: 300px;
}
.mini-layout {
  margin-bottom: 20px;
  padding: 9px;
}
.mini-layout div {
  -webkit-border-radius: 3px;
     -moz-border-radius: 3px;
          border-radius: 3px;
}
.mini-layout .mini-layout-body {
  background-color: #dceaf4;
  margin: 0 auto;
  width: 70%;
}
.mini-layout.fluid .mini-layout-sidebar,
.mini-layout.fluid .mini-layout-header,
.mini-layout.fluid .mini-layout-body {
  float: left;
}
.mini-layout.fluid .mini-layout-sidebar {
  background-color: #bbd8e9;
  width: 20%;
}
.mini-layout.fluid .mini-layout-body {
  width: 77.5%;
  margin-left: 2.5%;
}



/* Download page
-------------------------------------------------- */

.download .page-header {
  margin-top: 36px;
}
.page-header .toggle-all {
  margin-top: 5px;
}

/* Space out h3s when following a section */
.download h3 {
  margin-bottom: 5px;
}
.download-builder input + h3,
.download-builder .checkbox + h3 {
  margin-top: 9px;
}

/* Fields for variables */
.download-builder input[type=text] {
  margin-bottom: 9px;
  font-family: Menlo, Monaco, "Courier New", monospace;
  font-size: 12px;
  color: #d14;
}
.download-builder input[type=text]:focus {
  background-color: #fff;
}

/* Custom, larger checkbox labels */
.download .checkbox {
  padding: 6px 10px 6px 25px;
  font-size: 13px;
  line-height: 18px;
  color: #555;
  background-color: #f9f9f9;
  -webkit-border-radius: 3px;
     -moz-border-radius: 3px;
          border-radius: 3px;
  cursor: pointer;
}
.download .checkbox:hover {
  color: #333;
  background-color: #f5f5f5;
}
.download .checkbox small {
  font-size: 12px;
  color: #777;
}

/* Variables section */
#variables label {
  margin-bottom: 0;
}

/* Giant download button */
.download-btn {
  margin: 36px 0 108px;
}
#download p,
#download h4 {
  max-width: 50%;
  margin: 0 auto;
  color: #999;
  text-align: center;
}
#download h4 {
  margin-bottom: 0;
}
#download p {
  margin-bottom: 18px;
}
.download-btn .btn {
  display: block;
  width: auto;
  padding: 19px 24px;
  margin-bottom: 27px;
  font-size: 30px;
  line-height: 1;
  text-align: center;
  -webkit-border-radius: 6px;
     -moz-border-radius: 6px;
          border-radius: 6px;
}



/* Misc
-------------------------------------------------- */

/* Make tables spaced out a bit more */
h2 + table,
h3 + table,
h4 + table,
h2 + .row {
  margin-top: 5px;
}

/* Example sites showcase */
.example-sites {
  xmargin-left: 20px;
}
.example-sites img {
  max-width: 100%;
  margin: 0 auto;
}

.scrollspy-example {
  height: 200px;
  overflow: auto;
  position: relative;
}


/* Fake the :focus state to demo it */
.focused {
  border-color: rgba(82,168,236,.8);
  -webkit-box-shadow: inset 0 1px 3px rgba(0,0,0,.1), 0 0 8px rgba(82,168,236,.6);
     -moz-box-shadow: inset 0 1px 3px rgba(0,0,0,.1), 0 0 8px rgba(82,168,236,.6);
          box-shadow: inset 0 1px 3px rgba(0,0,0,.1), 0 0 8px rgba(82,168,236,.6);
  outline: 0;
}

/* For input sizes, make them display block */
.docs-input-sizes select,
.docs-input-sizes input[type=text] {
  display: block;
  margin-bottom: 9px;
}

/* Icons
------------------------- */
.the-icons {
  margin-left: 0;
  list-style: none;
}
.the-icons li {
  float: left;
  width: 25%;
  line-height: 25px;
}
.the-icons i:hover {
  background-color: rgba(255,0,0,.25);
}

/* Example page
------------------------- */
.bootstrap-examples h4 {
  margin: 10px 0 5px;
}
.bootstrap-examples p {
  font-size: 13px;
  line-height: 18px;
}
.bootstrap-examples .thumbnail {
  margin-bottom: 9px;
  background-color: #fff;
}



/* Bootstrap code examples
-------------------------------------------------- */

/* Base class */
.bs-docs-example {
  position: relative;
  margin: 15px 0;
  padding: 39px 19px 14px;
  *padding-top: 19px;
  background-color: #fff;
  border: 1px solid #ddd;
  -webkit-border-radius: 4px;
     -moz-border-radius: 4px;
          border-radius: 4px;
}

/* Echo out a label for the example */
.bs-docs-example:after {
  content: "Example";
  position: absolute;
  top: -1px;
  left: -1px;
  padding: 3px 7px;
  font-size: 12px;
  font-weight: bold;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  color: #9da0a4;
  -webkit-border-radius: 4px 0 4px 0;
     -moz-border-radius: 4px 0 4px 0;
          border-radius: 4px 0 4px 0;
}

/* Remove spacing between an example and it's code */
.bs-docs-example + .prettyprint {
  margin-top: -20px;
  padding-top: 15px;
}

/* Tweak examples
------------------------- */
.bs-docs-example > p:last-child {
  margin-bottom: 0;
}
.bs-docs-example .table,
.bs-docs-example .progress,
.bs-docs-example .well,
.bs-docs-example .alert,
.bs-docs-example .hero-unit,
.bs-docs-example .pagination,
.bs-docs-example .navbar,
.bs-docs-example > .nav,
.bs-docs-example blockquote {
  margin-bottom: 5px;
}
.bs-docs-example .pagination {
  margin-top: 0;
}
.bs-navbar-top-example,
.bs-navbar-bottom-example {
  z-index: 1;
  padding: 0;
  height: 90px;
  overflow: hidden; /* cut the drop shadows off */
}
.bs-navbar-top-example .navbar-fixed-top,
.bs-navbar-bottom-example .navbar-fixed-bottom {
  margin-left: 0;
  margin-right: 0;
}
.bs-navbar-top-example {
  -webkit-border-radius: 0 0 4px 4px;
     -moz-border-radius: 0 0 4px 4px;
          border-radius: 0 0 4px 4px;
}
.bs-navbar-top-example:after {
  top: auto;
  bottom: -1px;
  -webkit-border-radius: 0 4px 0 4px;
     -moz-border-radius: 0 4px 0 4px;
          border-radius: 0 4px 0 4px;
}
.bs-navbar-bottom-example {
  -webkit-border-radius: 4px 4px 0 0;
     -moz-border-radius: 4px 4px 0 0;
          border-radius: 4px 4px 0 0;
}
.bs-navbar-bottom-example .navbar {
  margin-bottom: 0;
}
form.bs-docs-example {
  padding-bottom: 19px;
}

/* Images */
.bs-docs-example-images img {
  margin: 10px;
  display: inline-block;
}

/* Tooltips */
.bs-docs-tooltip-examples {
  text-align: center;
  margin: 0 0 10px;
  list-style: none;
}
.bs-docs-tooltip-examples li {
  display: inline;
  padding: 0 10px;
}

/* Popovers */
.bs-docs-example-popover {
  padding-bottom: 24px;
  background-color: #f9f9f9;
}
.bs-docs-example-popover .popover {
  position: relative;
  display: block;
  float: left;
  width: 260px;
  margin: 20px;
}

/* Dropdowns */
.bs-docs-example-submenus {
  min-height: 180px;
}
.bs-docs-example-submenus > .pull-left + .pull-left {
  margin-left: 20px;
}
.bs-docs-example-submenus .dropup > .dropdown-menu,
.bs-docs-example-submenus .dropdown > .dropdown-menu {
  display: block;
  position: static;
  margin-bottom: 5px;
  *width: 180px;
}



/* Responsive docs
-------------------------------------------------- */

/* Utility classes table
------------------------- */
.responsive-utilities th small {
  display: block;
  font-weight: normal;
  color: #999;
}
.responsive-utilities tbody th {
  font-weight: normal;
}
.responsive-utilities td {
  text-align: center;
}
.responsive-utilities td.is-visible {
  color: #468847;
  background-color: #dff0d8 !important;
}
.responsive-utilities td.is-hidden {
  color: #ccc;
  background-color: #f9f9f9 !important;
}

/* Responsive tests
------------------------- */
.responsive-utilities-test {
  margin-top: 5px;
  margin-left: 0;
  list-style: none;
  overflow: hidden; /* clear floats */
}
.responsive-utilities-test li {
  position: relative;
  float: left;
  width: 25%;
  height: 43px;
  font-size: 14px;
  font-weight: bold;
  line-height: 43px;
  color: #999;
  text-align: center;
  border: 1px solid #ddd;
  -webkit-border-radius: 4px;
     -moz-border-radius: 4px;
          border-radius: 4px;
}
.responsive-utilities-test li + li {
  margin-left: 10px;
}
.responsive-utilities-test span {
  position: absolute;
  top:    -1px;
  left:   -1px;
  right:  -1px;
  bottom: -1px;
  -webkit-border-radius: 4px;
     -moz-border-radius: 4px;
          border-radius: 4px;
}
.responsive-utilities-test span {
  color: #468847;
  background-color: #dff0d8;
  border: 1px solid #d6e9c6;
}



/* Sidenav for Docs
-------------------------------------------------- */

.bs-docs-sidenav {
  width: 228px;
  margin: 30px 0 0;
  padding: 0;
  background-color: #fff;
  -webkit-border-radius: 6px;
     -moz-border-radius: 6px;
          border-radius: 6px;
  -webkit-box-shadow: 0 1px 4px rgba(0,0,0,.065);
     -moz-box-shadow: 0 1px 4px rgba(0,0,0,.065);
          box-shadow: 0 1px 4px rgba(0,0,0,.065);
}
.bs-docs-sidenav > li > a {
  display: block;
  width: 190px \9;
  margin: 0 0 -1px;
  padding: 8px 14px;
  border: 1px solid #e5e5e5;
}
.bs-docs-sidenav > li:first-child > a {
  -webkit-border-radius: 6px 6px 0 0;
     -moz-border-radius: 6px 6px 0 0;
          border-radius: 6px 6px 0 0;
}
.bs-docs-sidenav > li:last-child > a {
  -webkit-border-radius: 0 0 6px 6px;
     -moz-border-radius: 0 0 6px 6px;
          border-radius: 0 0 6px 6px;
}
.bs-docs-sidenav > .active > a {
  position: relative;
  z-index: 2;
  padding: 9px 15px;
  border: 0;
  text-shadow: 0 1px 0 rgba(0,0,0,.15);
  -webkit-box-shadow: inset 1px 0 0 rgba(0,0,0,.1), inset -1px 0 0 rgba(0,0,0,.1);
     -moz-box-shadow: inset 1px 0 0 rgba(0,0,0,.1), inset -1px 0 0 rgba(0,0,0,.1);
          box-shadow: inset 1px 0 0 rgba(0,0,0,.1), inset -1px 0 0 rgba(0,0,0,.1);
}
/* Chevrons */
.bs-docs-sidenav .icon-chevron-right {
  float: right;
  margin-top: 2px;
  margin-right: -6px;
  opacity: .25;
}
.bs-docs-sidenav > li > a:hover {
  background-color: #f5f5f5;
}
.bs-docs-sidenav a:hover .icon-chevron-right {
  opacity: .5;
}
.bs-docs-sidenav .active .icon-chevron-right,
.bs-docs-sidenav .active a:hover .icon-chevron-right {
  background-image: url(../img/glyphicons-halflings-white.png);
  opacity: 1;
}
.bs-docs-sidenav.affix {
  top: 40px;
}
.bs-docs-sidenav.affix-bottom {
  position: absolute;
  top: auto;
  bottom: 270px;
}

/* custom */
#github {
  display: block;
  position: fixed;
  width: 150px;
  height: 150px;
  top: 0;
  right: 0;
  z-index: 1050;
}

code,
pre {
  font-size: 13px;
}

/* Responsive
-------------------------------------------------- */

/* Desktop large
------------------------- */
@media (min-width: 1200px) {
  .bs-docs-container {
    max-width: 970px;
  }
  .bs-docs-sidenav {
    width: 258px;
  }
  .bs-docs-sidenav > li > a {
    width: 230px \9; /* Override the previous IE8-9 hack */
  }
}

/* Desktop
------------------------- */
@media (max-width: 980px) {
  /* Unfloat brand */
  body > .navbar-fixed-top .brand {
    float: left;
    margin-left: 0;
    padding-left: 10px;
    padding-right: 10px;
  }

  /* Inline-block quick links for more spacing */
  .quick-links li {
    display: inline-block;
    margin: 5px;
  }

  /* When affixed, space properly */
  .bs-docs-sidenav {
    top: 0;
    width: 218px;
    margin-top: 30px;
    margin-right: 0;
  }
}

/* Tablet to desktop
------------------------- */
@media (min-width: 768px) and (max-width: 979px) {
  /* Remove any padding from the body */
  body {
    padding-top: 0;
  }
  /* Widen masthead and social buttons to fill body padding */
  .jumbotron {
    margin-top: -20px; /* Offset bottom margin on .navbar */
  }
  /* Adjust sidenav width */
  .bs-docs-sidenav {
    width: 166px;
    margin-top: 20px;
  }
  .bs-docs-sidenav.affix {
    top: 0;
  }
}

/* Tablet
------------------------- */
@media (max-width: 767px) {
  /* Remove any padding from the body */
  body {
    padding-top: 0;
  }

  /* Widen masthead and social buttons to fill body padding */
  .jumbotron {
    padding: 70px 20px 20px 20px;
    margin-top:   -20px; /* Offset bottom margin on .navbar */
    margin-right: -20px;
    margin-left:  -20px;
  }
  .masthead h1 {
    font-size: 60px;
  }
  .masthead p,
  .masthead .btn {
    font-size: 20px;
  }
  .marketing .span4 {
    margin-bottom: 40px;
  }
  .bs-docs-social {
    margin: 0 -20px;
  }

  /* Space out the show-grid examples */
  .show-grid [class*="span"] {
    margin-bottom: 5px;
  }

  /* Sidenav */
  .bs-docs-sidenav {
    width: auto;
    margin-bottom: 20px;
  }
  .bs-docs-sidenav.affix {
    position: static;
    width: auto;
    top: 0;
  }

  /* Unfloat the back to top link in footer */
  .footer {
    margin-left: -20px;
    margin-right: -20px;
    padding-left: 20px;
    padding-right: 20px;
  }
  .footer p {
    margin-bottom: 9px;
  }

  #github {
    display: none !important;
  }
}

/* Landscape phones
------------------------- */
@media (max-width: 480px) {
  /* Remove padding above jumbotron */
  body {
    padding-top: 0;
  }

  /* Change up some type stuff */
  h2 small {
    display: block;
  }

  /* Downsize the jumbotrons */
  .jumbotron h1 {
    font-size: 45px;
  }
  .jumbotron p,
  .jumbotron .btn {
    font-size: 18px;
  }
  .jumbotron .btn {
    display: block;
    margin: 0 auto;
  }

  /* center align subhead text like the masthead */
  .subhead h1,
  .subhead p {
    text-align: center;
  }

  /* Marketing on home */
  .marketing h1 {
    font-size: 30px;
  }
  .marketing-byline {
    font-size: 18px;
  }

  /* center example sites */
  .example-sites {
    margin-left: 0;
  }
  .example-sites > li {
    float: none;
    display: block;
    max-width: 280px;
    margin: 0 auto 18px;
    text-align: center;
  }
  .example-sites .thumbnail > img {
    max-width: 270px;
  }

  /* Do our best to make tables work in narrow viewports */
  table code {
    white-space: normal;
    word-wrap: break-word;
    word-break: break-all;
  }

  /* Examples: dropdowns */
  .bs-docs-example-submenus > .pull-left {
    float: none;
    clear: both;
  }
  .bs-docs-example-submenus > .pull-left,
  .bs-docs-example-submenus > .pull-left + .pull-left {
    margin-left: 0;
  }
  .bs-docs-example-submenus p {
    margin-bottom: 0;
  }
  .bs-docs-example-submenus .dropup > .dropdown-menu,
  .bs-docs-example-submenus .dropdown > .dropdown-menu {
    margin-bottom: 10px;
    float: none;
    max-width: 180px;
  }

  /* Examples: modal */
  .modal-example .modal {
    position: relative;
    top: auto;
    right: auto;
    bottom: auto;
    left: auto;
  }

  /* Tighten up footer */
  .footer {
    padding-top: 20px;
    padding-bottom: 20px;
  }
}

/* custom */
#github {
  display: block;
  position: fixed;
  width: 150px;
  height: 150px;
  top: 0;
  right: 0;
  z-index: 1050;
}

code,
pre {
  font-size: 13px;
}